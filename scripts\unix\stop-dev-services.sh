#!/bin/bash

# ═══════════════════════════════════════════════════════════════════════════════
# 🛑 Notify Service API - Development Environment Cleanup (Unix/Linux/macOS)
# ═══════════════════════════════════════════════════════════════════════════════
# This script stops and optionally removes development containers

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
GRAY='\033[0;37m'
DARK_GRAY='\033[1;30m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Parse arguments
REMOVE=false
REMOVE_VOLUMES=false
FORCE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --remove|-r)
            REMOVE=true
            shift
            ;;
        --remove-volumes|-v)
            REMOVE_VOLUMES=true
            shift
            ;;
        --force|-f)
            FORCE=true
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "OPTIONS:"
            echo "  --remove, -r          Remove containers after stopping"
            echo "  --remove-volumes, -v  Remove volumes (⚠️ DATA LOSS!)"
            echo "  --force, -f           Skip confirmation prompts"
            echo "  --help, -h            Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                    # Stop containers only"
            echo "  $0 --remove           # Stop and remove containers"
            echo "  $0 -r -v -f           # Full cleanup with force"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Configuration
POSTGRES_CONTAINER="notify-postgres"
REDIS_CONTAINER="notify-redis"

# ASCII Art Header
echo ""
echo -e "${RED}    ███╗   ██╗ ██████╗ ████████╗██╗███████╗██╗   ██╗${NC}"
echo -e "${RED}    ████╗  ██║██╔═══██╗╚══██╔══╝██║██╔════╝╚██╗ ██╔╝${NC}"
echo -e "${RED}    ██╔██╗ ██║██║   ██║   ██║   ██║█████╗   ╚████╔╝ ${NC}"
echo -e "${RED}    ██║╚██╗██║██║   ██║   ██║   ██║██╔══╝    ╚██╔╝  ${NC}"
echo -e "${RED}    ██║ ╚████║╚██████╔╝   ██║   ██║██║        ██║   ${NC}"
echo -e "${RED}    ╚═╝  ╚═══╝ ╚═════╝    ╚═╝   ╚═╝╚═╝        ╚═╝   ${NC}"
echo ""
echo -e "${YELLOW}    🛑 Development Environment Cleanup${NC}"
echo -e "${GRAY}    🐳 Stopping Docker Containers${NC}"
echo ""
echo -e "${DARK_GRAY}═══════════════════════════════════════════════════════════════════════════════${NC}"

# Functions
log_step() {
    local message=$1
    local status=${2:-"INFO"}
    local timestamp=$(date '+%H:%M:%S')
    
    case $status in
        "SUCCESS") echo -e "[$timestamp] ✅ $message" ;;
        "ERROR")   echo -e "[$timestamp] ❌ $message" ;;
        "WARNING") echo -e "[$timestamp] ⚠️  $message" ;;
        "INFO")    echo -e "[$timestamp] 🔄 $message" ;;
    esac
}

log_section() {
    local title=$1
    echo ""
    echo -e "${DARK_GRAY}┌─────────────────────────────────────────────────────────────────────────────┐${NC}"
    echo -e "${WHITE}│ $(printf "%-75s" "$title") │${NC}"
    echo -e "${DARK_GRAY}└─────────────────────────────────────────────────────────────────────────────┘${NC}"
}

stop_container() {
    local container_name=$1
    local display_name=$2
    
    # Check if container exists
    if docker ps -a --filter "name=$container_name" --format "{{.Names}}" | grep -q "^$container_name$"; then
        # Check if container is running
        if docker ps --filter "name=$container_name" --format "{{.Names}}" | grep -q "^$container_name$"; then
            log_step "Stopping $display_name container..." "INFO"
            if docker stop "$container_name" &> /dev/null; then
                log_step "$display_name container stopped successfully" "SUCCESS"
            else
                log_step "Failed to stop $display_name container" "ERROR"
            fi
        else
            log_step "$display_name container is already stopped" "SUCCESS"
        fi
        
        if $REMOVE; then
            log_step "Removing $display_name container..." "INFO"
            if docker rm "$container_name" &> /dev/null; then
                log_step "$display_name container removed successfully" "SUCCESS"
            else
                log_step "Failed to remove $display_name container" "ERROR"
            fi
        fi
    else
        log_step "$display_name container does not exist" "WARNING"
    fi
}

# Show options
log_section "🔧 Cleanup Options"
if $REMOVE; then
    log_step "Mode: Stop and REMOVE containers" "WARNING"
else
    log_step "Mode: Stop containers only (containers will be preserved)" "INFO"
fi

if $REMOVE_VOLUMES; then
    log_step "Volumes: Will be REMOVED (data will be lost!)" "WARNING"
else
    log_step "Volumes: Will be preserved" "INFO"
fi

# Confirmation for destructive operations
if ($REMOVE || $REMOVE_VOLUMES) && ! $FORCE; then
    echo ""
    echo -e "${RED}⚠️  WARNING: This operation will permanently delete data!${NC}"
    read -p "Are you sure you want to continue? (y/N): " confirmation
    if [[ "$confirmation" != "y" && "$confirmation" != "Y" ]]; then
        log_step "Operation cancelled by user" "INFO"
        exit 0
    fi
fi

log_section "🛑 Stopping Services"

# Stop containers
stop_container "$POSTGRES_CONTAINER" "PostgreSQL"
stop_container "$REDIS_CONTAINER" "Redis"

# Remove volumes if requested
if $REMOVE_VOLUMES; then
    log_section "🗑️ Removing Volumes"
    
    log_step "Removing PostgreSQL volumes..." "INFO"
    pg_volumes=$(docker volume ls --filter "name=postgres" --format "{{.Name}}" 2>/dev/null || true)
    if [[ -n "$pg_volumes" ]]; then
        echo "$pg_volumes" | xargs docker volume rm &> /dev/null || true
        log_step "PostgreSQL volumes removed" "SUCCESS"
    else
        log_step "No PostgreSQL volumes found" "INFO"
    fi
    
    log_step "Removing Redis volumes..." "INFO"
    redis_volumes=$(docker volume ls --filter "name=redis" --format "{{.Name}}" 2>/dev/null || true)
    if [[ -n "$redis_volumes" ]]; then
        echo "$redis_volumes" | xargs docker volume rm &> /dev/null || true
        log_step "Redis volumes removed" "SUCCESS"
    else
        log_step "No Redis volumes found" "INFO"
    fi
fi

log_section "🧹 Cleanup Summary"

# Show final status
postgres_status=$(docker ps -a --filter "name=$POSTGRES_CONTAINER" --format "{{.Status}}" 2>/dev/null || echo "")
redis_status=$(docker ps -a --filter "name=$REDIS_CONTAINER" --format "{{.Status}}" 2>/dev/null || echo "")

if $REMOVE; then
    if [[ -z "$postgres_status" ]]; then
        log_step "PostgreSQL container: Removed" "SUCCESS"
    else
        log_step "PostgreSQL container: Still exists" "WARNING"
    fi
    
    if [[ -z "$redis_status" ]]; then
        log_step "Redis container: Removed" "SUCCESS"
    else
        log_step "Redis container: Still exists" "WARNING"
    fi
else
    postgres_display=$(echo "$postgres_status" | sed 's/Exited.*/Stopped/')
    redis_display=$(echo "$redis_status" | sed 's/Exited.*/Stopped/')
    log_step "PostgreSQL container: $postgres_display" "SUCCESS"
    log_step "Redis container: $redis_display" "SUCCESS"
fi

echo ""
echo -e "${GREEN}    🎉 Cleanup completed!${NC}"
echo ""
echo -e "${WHITE}    📝 Usage Examples:${NC}"
echo -e "${GRAY}    ├─ Stop only: ${CYAN}./scripts/unix/stop-dev-services.sh${NC}"
echo -e "${GRAY}    ├─ Stop & remove: ${YELLOW}./scripts/unix/stop-dev-services.sh --remove${NC}"
echo -e "${GRAY}    └─ Full cleanup: ${RED}./scripts/unix/stop-dev-services.sh --remove --remove-volumes --force${NC}"
echo ""
echo -e "${WHITE}    🚀 To restart services:${NC}"
echo -e "${GRAY}    └─ ${GREEN}./scripts/unix/start-dev-services.sh${NC}"
echo ""
echo -e "${DARK_GRAY}═══════════════════════════════════════════════════════════════════════════════${NC}"
