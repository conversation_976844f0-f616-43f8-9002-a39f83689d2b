﻿"restore":{"projectUniqueName":"C:\\Users\\<USER>\\OneDrive\\Documentos\\New folder\\Libraries\\Core\\Core.csproj","projectName":"Core","projectPath":"C:\\Users\\<USER>\\OneDrive\\Documentos\\New folder\\Libraries\\Core\\Core.csproj","outputPath":"C:\\Users\\<USER>\\OneDrive\\Documentos\\New folder\\Libraries\\Core\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["net9.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"C:\\Users\\<USER>\\OneDrive\\Documentos\\New folder\\Libraries\\Data\\Data.csproj":{"projectPath":"C:\\Users\\<USER>\\OneDrive\\Documentos\\New folder\\Libraries\\Data\\Data.csproj"},"C:\\Users\\<USER>\\OneDrive\\Documentos\\New folder\\Libraries\\Models\\Models.csproj":{"projectPath":"C:\\Users\\<USER>\\OneDrive\\Documentos\\New folder\\Libraries\\Models\\Models.csproj"},"C:\\Users\\<USER>\\OneDrive\\Documentos\\New folder\\Libraries\\Services\\Services.csproj":{"projectPath":"C:\\Users\\<USER>\\OneDrive\\Documentos\\New folder\\Libraries\\Services\\Services.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"10.0.100"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"MailKit":{"target":"Package","version":"[4.9.0, )"},"Microsoft.AspNetCore.SpaServices":{"target":"Package","version":"[9.0.6, )"},"Microsoft.AspNetCore.SpaServices.Extensions":{"target":"Package","version":"[9.0.6, )"},"Microsoft.EntityFrameworkCore.SqlServer":{"target":"Package","version":"[9.0.6, )"},"Microsoft.Extensions.Configuration":{"target":"Package","version":"[8.0.0, )"},"MimeKit":{"target":"Package","version":"[4.9.0, )"},"Swashbuckle.AspNetCore":{"target":"Package","version":"[7.2.0, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"downloadDependencies":[{"name":"Microsoft.AspNetCore.App.Ref","version":"[9.0.5, 9.0.5]"},{"name":"Microsoft.NETCore.App.Ref","version":"[9.0.5, 9.0.5]"},{"name":"Microsoft.WindowsDesktop.App.Ref","version":"[9.0.5, 9.0.5]"}],"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.5.25277.114/PortableRuntimeIdentifierGraph.json"}}