# ═══════════════════════════════════════════════════════════════════════════════
# ⚙️ Notify Service API - Configuration & Secrets Manager
# ═══════════════════════════════════════════════════════════════════════════════
# Interactive configuration management for environment variables and secrets

param(
    [switch]$Interactive,
    [switch]$Docker,
    [switch]$Local,
    [switch]$Production,
    [string]$ConfigFile
)

# Configuration
$ProjectRoot = Split-Path -Parent (Split-Path -Parent $MyInvocation.MyCommand.Path)
$EnvFile = Join-Path $ProjectRoot ".env"
$EnvTemplate = Join-Path $ProjectRoot ".env.template"
$SecretsFile = Join-Path $ProjectRoot "secrets.json"
$DockerComposeFile = Join-Path $ProjectRoot "docker-compose.yml"

function Show-Header {
    param([string]$Title = "Configuration Manager")
    Clear-Host
    Write-Host ""
    Write-Host "    ⚙️⚙️⚙️ NOTIFY SERVICE API ⚙️⚙️⚙️" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "    🔧 $Title" -ForegroundColor Yellow
    Write-Host "    🔐 Environment Variables • Secrets • Docker Config" -ForegroundColor Gray
    Write-Host ""
    Write-Host "═══════════════════════════════════════════════════════════════════════════════" -ForegroundColor DarkGray
}

function Write-Step {
    param([string]$Message, [string]$Status = "INFO")
    $timestamp = Get-Date -Format "HH:mm:ss"
    switch ($Status) {
        "SUCCESS" { Write-Host "[$timestamp] ✅ $Message" -ForegroundColor Green }
        "ERROR"   { Write-Host "[$timestamp] ❌ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "[$timestamp] ⚠️  $Message" -ForegroundColor Yellow }
        "INFO"    { Write-Host "[$timestamp] 🔄 $Message" -ForegroundColor Cyan }
        "INPUT"   { Write-Host "[$timestamp] 📝 $Message" -ForegroundColor Magenta }
    }
}

function Write-Section {
    param([string]$Title)
    Write-Host ""
    Write-Host "┌─────────────────────────────────────────────────────────────────────────────┐" -ForegroundColor DarkGray
    Write-Host "│ $($Title.PadRight(75)) │" -ForegroundColor White
    Write-Host "└─────────────────────────────────────────────────────────────────────────────┘" -ForegroundColor DarkGray
}

function Get-SecureInput {
    param([string]$Prompt, [string]$DefaultValue = "", [bool]$IsSecret = $false)
    
    if ($DefaultValue) {
        $displayDefault = if ($IsSecret) { "***" } else { $DefaultValue }
        Write-Host "    $Prompt [$displayDefault]: " -NoNewline -ForegroundColor Yellow
    } else {
        Write-Host "    $Prompt: " -NoNewline -ForegroundColor Yellow
    }
    
    if ($IsSecret) {
        $secureString = Read-Host -AsSecureString
        $value = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($secureString))
    } else {
        $value = Read-Host
    }
    
    return if ($value) { $value } else { $DefaultValue }
}

function Initialize-ConfigFiles {
    Write-Section "📁 Configuration File Setup"
    
    # Create .env from template if it doesn't exist
    if (-not (Test-Path $EnvFile)) {
        if (Test-Path $EnvTemplate) {
            Write-Step "Creating .env file from template..." "INFO"
            Copy-Item $EnvTemplate $EnvFile
            Write-Step ".env file created successfully" "SUCCESS"
        } else {
            Write-Step "Creating default .env file..." "INFO"
            Create-DefaultEnvFile
            Write-Step "Default .env file created" "SUCCESS"
        }
    } else {
        Write-Step ".env file already exists" "SUCCESS"
    }
    
    # Create secrets.json if it doesn't exist
    if (-not (Test-Path $SecretsFile)) {
        Write-Step "Creating secrets.json file..." "INFO"
        Create-DefaultSecretsFile
        Write-Step "secrets.json file created" "SUCCESS"
    } else {
        Write-Step "secrets.json file already exists" "SUCCESS"
    }
}

function Create-DefaultEnvFile {
    $defaultEnv = @"
# ═══════════════════════════════════════════════════════════════════════════════
# 🔧 Notify Service API - Environment Configuration
# ═══════════════════════════════════════════════════════════════════════════════

# Application Configuration
ASPNETCORE_ENVIRONMENT=Development
API_PORT=8080
ASPNETCORE_URLS=https://+:443;http://+:80

# Database Configuration
POSTGRES_PASSWORD=********
POSTGRES_USER=********
POSTGRES_DB=NotifyDb
POSTGRES_PORT=5432
POSTGRES_HOST=localhost

# Redis Configuration
REDIS_PASSWORD=
REDIS_PORT=6379
REDIS_HOST=localhost

# JWT Configuration (CHANGE IN PRODUCTION!)
JWT_SECRET_KEY=notify-service-development-key-change-in-production-32-chars-minimum

# Email Configuration
MAIL_FROM=<EMAIL>
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
MAIL_DISPLAY_NAME=Notify Service

# Docker Configuration
DOCKER_REGISTRY=notifyregistry.azurecr.io
IMAGE_NAME=notify-service-api

# Azure Configuration (for deployment)
AZURE_SUBSCRIPTION_ID=
AZURE_RESOURCE_GROUP=notify-service-rg
AZURE_WEBAPP_NAME=notify-service-api
AZURE_CONTAINER_REGISTRY=notifyregistry.azurecr.io

# Monitoring Configuration
SLACK_WEBHOOK_URL=
APPLICATION_INSIGHTS_KEY=

# Development Tools
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin123
"@
    
    Set-Content -Path $EnvFile -Value $defaultEnv -Encoding UTF8
}

function Create-DefaultSecretsFile {
    $defaultSecrets = @{
        "ConnectionStrings" = @{
            "DefaultConnection" = "Host=localhost;Database=NotifyDb;Username=********;Password=********"
            "IdentityConnection" = "Host=localhost;Database=NotifyIdentityDb;Username=********;Password=********"
        }
        "JWTSettings" = @{
            "Key" = "notify-service-development-key-change-in-production-32-chars-minimum"
            "Issuer" = "NotifyServiceAPI"
            "Audience" = "NotifyServiceUsers"
            "DurationInMinutes" = 60
        }
        "MailSettings" = @{
            "EmailFrom" = "<EMAIL>"
            "SmtpHost" = "smtp.gmail.com"
            "SmtpPort" = 587
            "SmtpUser" = ""
            "SmtpPass" = ""
            "DisplayName" = "Notify Service"
        }
        "RedisSettings" = @{
            "RedisConnectionString" = "localhost:6379"
            "CacheTime" = 30
            "RedisDatabaseId" = 0
        }
    }
    
    $defaultSecrets | ConvertTo-Json -Depth 10 | Set-Content -Path $SecretsFile -Encoding UTF8
}

function Show-ConfigurationMenu {
    Write-Section "🎛️ Configuration Options"
    Write-Host ""
    Write-Host "    [1] 🔧 Quick Setup (Recommended)" -ForegroundColor Green
    Write-Host "        └─ Interactive setup for development environment"
    Write-Host ""
    Write-Host "    [2] 🗄️  Database Configuration" -ForegroundColor Cyan
    Write-Host "        └─ Configure PostgreSQL connection settings"
    Write-Host ""
    Write-Host "    [3] 🔴 Redis Configuration" -ForegroundColor Red
    Write-Host "        └─ Configure Redis cache settings"
    Write-Host ""
    Write-Host "    [4] 📧 Email Configuration" -ForegroundColor Blue
    Write-Host "        └─ Configure SMTP settings for notifications"
    Write-Host ""
    Write-Host "    [5] 🔐 JWT & Security" -ForegroundColor Magenta
    Write-Host "        └─ Configure authentication and security settings"
    Write-Host ""
    Write-Host "    [6] 🐳 Docker Configuration" -ForegroundColor Yellow
    Write-Host "        └─ Configure Docker and container settings"
    Write-Host ""
    Write-Host "    [7] ☁️  Azure Configuration" -ForegroundColor Cyan
    Write-Host "        └─ Configure Azure deployment settings"
    Write-Host ""
    Write-Host "    [8] 📊 Monitoring & Logging" -ForegroundColor Gray
    Write-Host "        └─ Configure monitoring and logging services"
    Write-Host ""
    Write-Host "    [9] 📄 View Current Configuration" -ForegroundColor White
    Write-Host "        └─ Display current environment variables"
    Write-Host ""
    Write-Host "    [10] 💾 Export Configuration" -ForegroundColor Green
    Write-Host "         └─ Export settings for different environments"
    Write-Host ""
    Write-Host "    [11] 📥 Import Configuration" -ForegroundColor Blue
    Write-Host "         └─ Import settings from file"
    Write-Host ""
    Write-Host "    [0] 🚪 Exit" -ForegroundColor Gray
    Write-Host ""
}

function Configure-QuickSetup {
    Write-Section "🚀 Quick Development Setup"
    
    Write-Host "    This will configure basic settings for local development." -ForegroundColor Gray
    Write-Host ""
    
    # Database settings
    Write-Host "    🗄️ Database Configuration:" -ForegroundColor Cyan
    $dbPassword = Get-SecureInput "PostgreSQL Password" "********" $true
    $dbHost = Get-SecureInput "PostgreSQL Host" "localhost"
    $dbPort = Get-SecureInput "PostgreSQL Port" "5432"
    
    # Redis settings
    Write-Host ""
    Write-Host "    🔴 Redis Configuration:" -ForegroundColor Red
    $redisHost = Get-SecureInput "Redis Host" "localhost"
    $redisPort = Get-SecureInput "Redis Port" "6379"
    $redisPassword = Get-SecureInput "Redis Password (optional)" "" $true
    
    # JWT settings
    Write-Host ""
    Write-Host "    🔐 Security Configuration:" -ForegroundColor Magenta
    $jwtKey = Get-SecureInput "JWT Secret Key (min 32 chars)" (Generate-SecureKey) $true
    
    # Email settings
    Write-Host ""
    Write-Host "    📧 Email Configuration:" -ForegroundColor Blue
    $emailFrom = Get-SecureInput "From Email Address" "<EMAIL>"
    $smtpHost = Get-SecureInput "SMTP Host" "smtp.gmail.com"
    $smtpPort = Get-SecureInput "SMTP Port" "587"
    $smtpUser = Get-SecureInput "SMTP Username (optional)" ""
    $smtpPassword = Get-SecureInput "SMTP Password (optional)" "" $true
    
    # Update configuration files
    Update-EnvironmentFile @{
        "POSTGRES_PASSWORD" = $dbPassword
        "POSTGRES_HOST" = $dbHost
        "POSTGRES_PORT" = $dbPort
        "REDIS_HOST" = $redisHost
        "REDIS_PORT" = $redisPort
        "REDIS_PASSWORD" = $redisPassword
        "JWT_SECRET_KEY" = $jwtKey
        "MAIL_FROM" = $emailFrom
        "SMTP_HOST" = $smtpHost
        "SMTP_PORT" = $smtpPort
        "SMTP_USER" = $smtpUser
        "SMTP_PASSWORD" = $smtpPassword
    }
    
    Update-SecretsFile $dbPassword $jwtKey $smtpUser $smtpPassword $redisPassword
    
    Write-Step "Quick setup completed successfully!" "SUCCESS"
    Write-Host ""
    Write-Host "    📝 Next steps:" -ForegroundColor White
    Write-Host "    1. Review the generated .env file" -ForegroundColor Gray
    Write-Host "    2. Run: .\scripts\start-dev-services.ps1" -ForegroundColor Gray
    Write-Host ""
}

function Generate-SecureKey {
    $bytes = New-Object byte[] 32
    [System.Security.Cryptography.RNGCryptoServiceProvider]::Create().GetBytes($bytes)
    return [Convert]::ToBase64String($bytes)
}

function Update-EnvironmentFile {
    param([hashtable]$Variables)
    
    if (Test-Path $EnvFile) {
        $content = Get-Content $EnvFile
    } else {
        $content = @()
    }
    
    foreach ($key in $Variables.Keys) {
        $value = $Variables[$key]
        $pattern = "^$key="
        $newLine = "$key=$value"
        
        $found = $false
        for ($i = 0; $i -lt $content.Length; $i++) {
            if ($content[$i] -match $pattern) {
                $content[$i] = $newLine
                $found = $true
                break
            }
        }
        
        if (-not $found) {
            $content += $newLine
        }
    }
    
    Set-Content -Path $EnvFile -Value $content -Encoding UTF8
}

function Update-SecretsFile {
    param(
        [string]$DbPassword,
        [string]$JwtKey,
        [string]$SmtpUser,
        [string]$SmtpPassword,
        [string]$RedisPassword
    )
    
    $secrets = @{
        "ConnectionStrings" = @{
            "DefaultConnection" = "Host=localhost;Database=NotifyDb;Username=********;Password=$DbPassword"
            "IdentityConnection" = "Host=localhost;Database=NotifyIdentityDb;Username=********;Password=$DbPassword"
        }
        "JWTSettings" = @{
            "Key" = $JwtKey
            "Issuer" = "NotifyServiceAPI"
            "Audience" = "NotifyServiceUsers"
            "DurationInMinutes" = 60
        }
        "MailSettings" = @{
            "EmailFrom" = "<EMAIL>"
            "SmtpHost" = "smtp.gmail.com"
            "SmtpPort" = 587
            "SmtpUser" = $SmtpUser
            "SmtpPass" = $SmtpPassword
            "DisplayName" = "Notify Service"
        }
        "RedisSettings" = @{
            "RedisConnectionString" = if ($RedisPassword) { "localhost:6379,password=$RedisPassword" } else { "localhost:6379" }
            "CacheTime" = 30
            "RedisDatabaseId" = 0
        }
    }
    
    $secrets | ConvertTo-Json -Depth 10 | Set-Content -Path $SecretsFile -Encoding UTF8
}

function Show-CurrentConfiguration {
    Write-Section "📄 Current Configuration"

    if (Test-Path $EnvFile) {
        Write-Host "    📁 Environment Variables (.env):" -ForegroundColor Cyan
        $envContent = Get-Content $EnvFile | Where-Object { $_ -notmatch "^#" -and $_ -ne "" }
        foreach ($line in $envContent) {
            if ($line -match "PASSWORD|SECRET|KEY") {
                $parts = $line -split "=", 2
                Write-Host "    $($parts[0])=" -NoNewline -ForegroundColor Gray
                Write-Host "***" -ForegroundColor Yellow
            } else {
                Write-Host "    $line" -ForegroundColor Gray
            }
        }
    } else {
        Write-Host "    ❌ .env file not found" -ForegroundColor Red
    }

    Write-Host ""
    if (Test-Path $SecretsFile) {
        Write-Host "    🔐 Secrets Configuration:" -ForegroundColor Magenta
        Write-Host "    ✅ secrets.json exists (contains sensitive data)" -ForegroundColor Green
    } else {
        Write-Host "    ❌ secrets.json file not found" -ForegroundColor Red
    }
}

function Export-Configuration {
    param([string]$Environment = "development")

    Write-Section "💾 Export Configuration"

    $exportPath = Join-Path $ProjectRoot "config-exports"
    if (-not (Test-Path $exportPath)) {
        New-Item -ItemType Directory -Path $exportPath | Out-Null
    }

    $timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
    $exportFile = Join-Path $exportPath "config-$Environment-$timestamp.json"

    $config = @{
        "Environment" = $Environment
        "Timestamp" = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
        "EnvironmentVariables" = @{}
        "Secrets" = @{}
    }

    # Export environment variables
    if (Test-Path $EnvFile) {
        $envContent = Get-Content $EnvFile | Where-Object { $_ -notmatch "^#" -and $_ -ne "" }
        foreach ($line in $envContent) {
            $parts = $line -split "=", 2
            if ($parts.Length -eq 2) {
                $config.EnvironmentVariables[$parts[0]] = $parts[1]
            }
        }
    }

    # Export secrets (masked)
    if (Test-Path $SecretsFile) {
        $secrets = Get-Content $SecretsFile | ConvertFrom-Json
        $config.Secrets = $secrets
    }

    $config | ConvertTo-Json -Depth 10 | Set-Content -Path $exportFile -Encoding UTF8

    Write-Step "Configuration exported to: $exportFile" "SUCCESS"
}

function Import-Configuration {
    Write-Section "📥 Import Configuration"

    Write-Host "    📁 Available configuration files:" -ForegroundColor Cyan
    $exportPath = Join-Path $ProjectRoot "config-exports"

    if (Test-Path $exportPath) {
        $configFiles = Get-ChildItem -Path $exportPath -Filter "*.json" | Sort-Object LastWriteTime -Descending

        if ($configFiles.Count -gt 0) {
            for ($i = 0; $i -lt $configFiles.Count; $i++) {
                Write-Host "    [$($i + 1)] $($configFiles[$i].Name)" -ForegroundColor Gray
            }

            Write-Host ""
            $choice = Read-Host "    Select file number (or press Enter to cancel)"

            if ($choice -and $choice -match "^\d+$" -and [int]$choice -le $configFiles.Count) {
                $selectedFile = $configFiles[[int]$choice - 1]
                Import-ConfigurationFile -FilePath $selectedFile.FullName
            } else {
                Write-Step "Import cancelled" "INFO"
            }
        } else {
            Write-Host "    No configuration files found" -ForegroundColor Yellow
        }
    } else {
        Write-Host "    No exports directory found" -ForegroundColor Yellow
    }
}

function Import-ConfigurationFile {
    param([string]$FilePath)

    try {
        $config = Get-Content $FilePath | ConvertFrom-Json

        Write-Step "Importing configuration from: $(Split-Path $FilePath -Leaf)" "INFO"

        # Import environment variables
        if ($config.EnvironmentVariables) {
            Update-EnvironmentFile $config.EnvironmentVariables
            Write-Step "Environment variables imported" "SUCCESS"
        }

        # Import secrets
        if ($config.Secrets) {
            $config.Secrets | ConvertTo-Json -Depth 10 | Set-Content -Path $SecretsFile -Encoding UTF8
            Write-Step "Secrets imported" "SUCCESS"
        }

        Write-Step "Configuration import completed" "SUCCESS"

    } catch {
        Write-Step "Failed to import configuration: $($_.Exception.Message)" "ERROR"
    }
}

function Configure-Docker {
    Write-Section "🐳 Docker Configuration"

    Write-Host "    Configure Docker and container settings:" -ForegroundColor Gray
    Write-Host ""

    $dockerRegistry = Get-SecureInput "Docker Registry" "notifyregistry.azurecr.io"
    $imageName = Get-SecureInput "Image Name" "notify-service-api"
    $apiPort = Get-SecureInput "API Port" "8080"
    $********Port = Get-SecureInput "PostgreSQL Port" "5432"
    $redisPort = Get-SecureInput "Redis Port" "6379"

    Update-EnvironmentFile @{
        "DOCKER_REGISTRY" = $dockerRegistry
        "IMAGE_NAME" = $imageName
        "API_PORT" = $apiPort
        "POSTGRES_PORT" = $********Port
        "REDIS_PORT" = $redisPort
    }

    Write-Step "Docker configuration updated" "SUCCESS"
}

function Configure-Azure {
    Write-Section "☁️ Azure Configuration"

    Write-Host "    Configure Azure deployment settings:" -ForegroundColor Gray
    Write-Host ""

    $subscriptionId = Get-SecureInput "Azure Subscription ID" ""
    $resourceGroup = Get-SecureInput "Resource Group Name" "notify-service-rg"
    $webAppName = Get-SecureInput "Web App Name" "notify-service-api"
    $containerRegistry = Get-SecureInput "Container Registry" "notifyregistry.azurecr.io"

    Update-EnvironmentFile @{
        "AZURE_SUBSCRIPTION_ID" = $subscriptionId
        "AZURE_RESOURCE_GROUP" = $resourceGroup
        "AZURE_WEBAPP_NAME" = $webAppName
        "AZURE_CONTAINER_REGISTRY" = $containerRegistry
    }

    Write-Step "Azure configuration updated" "SUCCESS"
}

function Configure-Database {
    Write-Section "🗄️ Database Configuration"

    Write-Host "    Configure PostgreSQL database settings:" -ForegroundColor Gray
    Write-Host ""

    $dbHost = Get-SecureInput "PostgreSQL Host" "localhost"
    $dbPort = Get-SecureInput "PostgreSQL Port" "5432"
    $dbUser = Get-SecureInput "PostgreSQL Username" "********"
    $dbPassword = Get-SecureInput "PostgreSQL Password" "********" $true
    $dbName = Get-SecureInput "Database Name" "NotifyDb"
    $identityDbName = Get-SecureInput "Identity Database Name" "NotifyIdentityDb"

    Update-EnvironmentFile @{
        "POSTGRES_HOST" = $dbHost
        "POSTGRES_PORT" = $dbPort
        "POSTGRES_USER" = $dbUser
        "POSTGRES_PASSWORD" = $dbPassword
        "POSTGRES_DB" = $dbName
    }

    # Update secrets file with connection strings
    $defaultConnection = "Host=$dbHost;Port=$dbPort;Database=$dbName;Username=$dbUser;Password=$dbPassword"
    $identityConnection = "Host=$dbHost;Port=$dbPort;Database=$identityDbName;Username=$dbUser;Password=$dbPassword"

    if (Test-Path $SecretsFile) {
        $secrets = Get-Content $SecretsFile | ConvertFrom-Json
        $secrets.ConnectionStrings.DefaultConnection = $defaultConnection
        $secrets.ConnectionStrings.IdentityConnection = $identityConnection
        $secrets | ConvertTo-Json -Depth 10 | Set-Content -Path $SecretsFile -Encoding UTF8
    }

    Write-Step "Database configuration updated" "SUCCESS"
}

function Configure-Redis {
    Write-Section "🔴 Redis Configuration"

    Write-Host "    Configure Redis cache settings:" -ForegroundColor Gray
    Write-Host ""

    $redisHost = Get-SecureInput "Redis Host" "localhost"
    $redisPort = Get-SecureInput "Redis Port" "6379"
    $redisPassword = Get-SecureInput "Redis Password (optional)" "" $true
    $redisDatabaseId = Get-SecureInput "Redis Database ID" "0"
    $cacheTime = Get-SecureInput "Default Cache Time (minutes)" "30"

    $connectionString = if ($redisPassword) {
        "$redisHost:$redisPort,password=$redisPassword"
    } else {
        "$redisHost:$redisPort"
    }

    Update-EnvironmentFile @{
        "REDIS_HOST" = $redisHost
        "REDIS_PORT" = $redisPort
        "REDIS_PASSWORD" = $redisPassword
    }

    # Update secrets file
    if (Test-Path $SecretsFile) {
        $secrets = Get-Content $SecretsFile | ConvertFrom-Json
        $secrets.RedisSettings.RedisConnectionString = $connectionString
        $secrets.RedisSettings.RedisDatabaseId = [int]$redisDatabaseId
        $secrets.RedisSettings.CacheTime = [int]$cacheTime
        $secrets | ConvertTo-Json -Depth 10 | Set-Content -Path $SecretsFile -Encoding UTF8
    }

    Write-Step "Redis configuration updated" "SUCCESS"
}

function Configure-Email {
    Write-Section "📧 Email Configuration"

    Write-Host "    Configure SMTP email settings:" -ForegroundColor Gray
    Write-Host ""

    Write-Host "    📋 Common SMTP Providers:" -ForegroundColor Cyan
    Write-Host "    ├─ Gmail: smtp.gmail.com:587 (use App Password)" -ForegroundColor Gray
    Write-Host "    ├─ Outlook: smtp-mail.outlook.com:587" -ForegroundColor Gray
    Write-Host "    ├─ SendGrid: smtp.sendgrid.net:587" -ForegroundColor Gray
    Write-Host "    └─ Custom: your-smtp-server.com:587" -ForegroundColor Gray
    Write-Host ""

    $emailFrom = Get-SecureInput "From Email Address" "<EMAIL>"
    $smtpHost = Get-SecureInput "SMTP Host" "smtp.gmail.com"
    $smtpPort = Get-SecureInput "SMTP Port" "587"
    $smtpUser = Get-SecureInput "SMTP Username" ""
    $smtpPassword = Get-SecureInput "SMTP Password" "" $true
    $displayName = Get-SecureInput "Display Name" "Notify Service"

    Update-EnvironmentFile @{
        "MAIL_FROM" = $emailFrom
        "SMTP_HOST" = $smtpHost
        "SMTP_PORT" = $smtpPort
        "SMTP_USER" = $smtpUser
        "SMTP_PASSWORD" = $smtpPassword
        "MAIL_DISPLAY_NAME" = $displayName
    }

    # Update secrets file
    if (Test-Path $SecretsFile) {
        $secrets = Get-Content $SecretsFile | ConvertFrom-Json
        $secrets.MailSettings.EmailFrom = $emailFrom
        $secrets.MailSettings.SmtpHost = $smtpHost
        $secrets.MailSettings.SmtpPort = [int]$smtpPort
        $secrets.MailSettings.SmtpUser = $smtpUser
        $secrets.MailSettings.SmtpPass = $smtpPassword
        $secrets.MailSettings.DisplayName = $displayName
        $secrets | ConvertTo-Json -Depth 10 | Set-Content -Path $SecretsFile -Encoding UTF8
    }

    Write-Step "Email configuration updated" "SUCCESS"
}

function Configure-Security {
    Write-Section "� JWT & Security Configuration"

    Write-Host "    Configure authentication and security settings:" -ForegroundColor Gray
    Write-Host ""

    $currentKey = ""
    if (Test-Path $SecretsFile) {
        $secrets = Get-Content $SecretsFile | ConvertFrom-Json
        $currentKey = $secrets.JWTSettings.Key
    }

    $jwtKey = Get-SecureInput "JWT Secret Key (min 32 chars)" $currentKey $true
    $issuer = Get-SecureInput "JWT Issuer" "NotifyServiceAPI"
    $audience = Get-SecureInput "JWT Audience" "NotifyServiceUsers"
    $duration = Get-SecureInput "Token Duration (minutes)" "60"

    # Validate JWT key length
    if ($jwtKey.Length -lt 32) {
        Write-Step "JWT key too short, generating secure key..." "WARNING"
        $jwtKey = Generate-SecureKey
        Write-Step "Generated secure JWT key" "SUCCESS"
    }

    Update-EnvironmentFile @{
        "JWT_SECRET_KEY" = $jwtKey
    }

    # Update secrets file
    if (Test-Path $SecretsFile) {
        $secrets = Get-Content $SecretsFile | ConvertFrom-Json
        $secrets.JWTSettings.Key = $jwtKey
        $secrets.JWTSettings.Issuer = $issuer
        $secrets.JWTSettings.Audience = $audience
        $secrets.JWTSettings.DurationInMinutes = [int]$duration
        $secrets | ConvertTo-Json -Depth 10 | Set-Content -Path $SecretsFile -Encoding UTF8
    }

    Write-Step "Security configuration updated" "SUCCESS"
}

function Configure-Monitoring {
    Write-Section "�📊 Monitoring & Logging Configuration"

    Write-Host "    Configure monitoring and logging services:" -ForegroundColor Gray
    Write-Host ""

    $slackWebhook = Get-SecureInput "Slack Webhook URL (optional)" ""
    $appInsightsKey = Get-SecureInput "Application Insights Key (optional)" ""
    $seqUrl = Get-SecureInput "Seq Logging URL (optional)" "http://localhost:5341"

    Update-EnvironmentFile @{
        "SLACK_WEBHOOK_URL" = $slackWebhook
        "APPLICATION_INSIGHTS_KEY" = $appInsightsKey
        "SEQ_URL" = $seqUrl
    }

    Write-Step "Monitoring configuration updated" "SUCCESS"
}

# Main execution
if ($Interactive -or (-not $Docker -and -not $Local -and -not $Production -and -not $ConfigFile)) {
    do {
        Show-Header
        Show-ConfigurationMenu

        Write-Host "    👉 Enter your choice (0-11): " -NoNewline -ForegroundColor White
        $choice = Read-Host

        switch ($choice) {
            "1" { Configure-QuickSetup; Pause }
            "2" { Configure-Database; Pause }
            "3" { Configure-Redis; Pause }
            "4" { Configure-Email; Pause }
            "5" { Configure-Security; Pause }
            "6" { Configure-Docker; Pause }
            "7" { Configure-Azure; Pause }
            "8" { Configure-Monitoring; Pause }
            "9" { Show-CurrentConfiguration; Pause }
            "10" { Export-Configuration; Pause }
            "11" { Import-Configuration; Pause }
            "0" { Write-Host "👋 Configuration saved!" -ForegroundColor Green; exit 0 }
            default { Write-Host "❌ Invalid choice" -ForegroundColor Red; Start-Sleep 2 }
        }

    } while ($choice -ne "0")
} else {
    # Non-interactive mode
    Initialize-ConfigFiles

    if ($Docker) {
        Configure-Docker
    }

    if ($Local) {
        Configure-QuickSetup
    }

    if ($Production) {
        # Production-specific configuration
        Write-Section "🏭 Production Configuration"
        Write-Step "Production configuration mode - please review all settings carefully" "WARNING"
    }

    if ($ConfigFile) {
        Import-ConfigurationFile -FilePath $ConfigFile
    }
}
