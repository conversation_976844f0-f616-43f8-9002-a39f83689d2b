# ═══════════════════════════════════════════════════════════════════════════════
# 🚀 Notify Service API - Cross-Platform Development Setup Launcher
# ═══════════════════════════════════════════════════════════════════════════════
# Detects platform and launches appropriate development tools

param(
    [switch]$Menu,
    [switch]$Start,
    [switch]$Stop,
    [switch]$Config,
    [switch]$Help
)

# Colors
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Cyan = "Cyan"
    Magenta = "Magenta"
    Gray = "Gray"
    White = "White"
}

function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    Write-Host $Text -ForegroundColor $Colors[$Color]
}

function Show-Header {
    Clear-Host
    Write-Host ""
    Write-ColorText "    ███╗   ██╗ ██████╗ ████████╗██╗███████╗██╗   ██╗" "Cyan"
    Write-ColorText "    ████╗  ██║██╔═══██╗╚══██╔══╝██║██╔════╝╚██╗ ██╔╝" "Cyan"
    Write-ColorText "    ██╔██╗ ██║██║   ██║   ██║   ██║█████╗   ╚████╔╝ " "Cyan"
    Write-ColorText "    ██║╚██╗██║██║   ██║   ██║   ██║██╔══╝    ╚██╔╝  " "Cyan"
    Write-ColorText "    ██║ ╚████║╚██████╔╝   ██║   ██║██║        ██║   " "Cyan"
    Write-ColorText "    ╚═╝  ╚═══╝ ╚═════╝    ╚═╝   ╚═╝╚═╝        ╚═╝   " "Cyan"
    Write-Host ""
    Write-ColorText "    🚀 Cross-Platform Development Setup" "Yellow"
    Write-ColorText "    🎛️ Windows • Linux • macOS Support" "Gray"
    Write-Host ""
    Write-ColorText "═══════════════════════════════════════════════════════════════════════════════" "Gray"
}

function Get-Platform {
    if ($IsWindows -or $env:OS -eq "Windows_NT") {
        return "Windows"
    } elseif ($IsLinux) {
        return "Linux"
    } elseif ($IsMacOS) {
        return "macOS"
    } else {
        return "Unknown"
    }
}

function Get-ScriptPath {
    param([string]$ScriptName, [string]$Platform)
    
    $scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
    
    switch ($Platform) {
        "Windows" {
            return Join-Path $scriptDir "$ScriptName.ps1"
        }
        { $_ -in @("Linux", "macOS") } {
            return Join-Path $scriptDir "unix" "$ScriptName.sh"
        }
        default {
            throw "Unsupported platform: $Platform"
        }
    }
}

function Test-Dependencies {
    param([string]$Platform)
    
    Write-ColorText "🔍 Checking dependencies for $Platform..." "Blue"
    
    $missing = @()
    
    # Check Docker
    try {
        docker --version | Out-Null
        Write-ColorText "✅ Docker: Available" "Green"
    } catch {
        Write-ColorText "❌ Docker: Not found" "Red"
        $missing += "Docker"
    }
    
    # Check .NET SDK
    try {
        dotnet --version | Out-Null
        $version = dotnet --version
        Write-ColorText "✅ .NET SDK: $version" "Green"
    } catch {
        Write-ColorText "❌ .NET SDK: Not found" "Red"
        $missing += ".NET SDK"
    }
    
    # Check Git
    try {
        git --version | Out-Null
        Write-ColorText "✅ Git: Available" "Green"
    } catch {
        Write-ColorText "⚠️ Git: Not found (optional)" "Yellow"
    }
    
    if ($missing.Count -gt 0) {
        Write-Host ""
        Write-ColorText "❌ Missing required dependencies:" "Red"
        foreach ($dep in $missing) {
            Write-ColorText "   - $dep" "Red"
        }
        Write-Host ""
        Show-InstallInstructions -Platform $Platform -Dependencies $missing
        return $false
    }
    
    Write-ColorText "✅ All dependencies satisfied!" "Green"
    return $true
}

function Show-InstallInstructions {
    param([string]$Platform, [array]$Dependencies)
    
    Write-ColorText "📥 Installation Instructions:" "Yellow"
    Write-Host ""
    
    switch ($Platform) {
        "Windows" {
            if ("Docker" -in $Dependencies) {
                Write-ColorText "🐳 Docker Desktop:" "Cyan"
                Write-ColorText "   Download: https://www.docker.com/products/docker-desktop" "Gray"
                Write-ColorText "   Or use: winget install Docker.DockerDesktop" "Gray"
                Write-Host ""
            }
            if (".NET SDK" -in $Dependencies) {
                Write-ColorText "🔧 .NET 8 SDK:" "Cyan"
                Write-ColorText "   Download: https://dotnet.microsoft.com/download/dotnet/8.0" "Gray"
                Write-ColorText "   Or use: winget install Microsoft.DotNet.SDK.8" "Gray"
                Write-Host ""
            }
        }
        "Linux" {
            if ("Docker" -in $Dependencies) {
                Write-ColorText "🐳 Docker:" "Cyan"
                Write-ColorText "   Ubuntu/Debian: sudo apt-get install docker.io" "Gray"
                Write-ColorText "   CentOS/RHEL: sudo yum install docker" "Gray"
                Write-Host ""
            }
            if (".NET SDK" -in $Dependencies) {
                Write-ColorText "🔧 .NET 8 SDK:" "Cyan"
                Write-ColorText "   wget https://dot.net/v1/dotnet-install.sh" "Gray"
                Write-ColorText "   chmod +x dotnet-install.sh" "Gray"
                Write-ColorText "   ./dotnet-install.sh --channel 8.0" "Gray"
                Write-Host ""
            }
        }
        "macOS" {
            if ("Docker" -in $Dependencies) {
                Write-ColorText "🐳 Docker Desktop:" "Cyan"
                Write-ColorText "   Download: https://www.docker.com/products/docker-desktop" "Gray"
                Write-ColorText "   Or use: brew install --cask docker" "Gray"
                Write-Host ""
            }
            if (".NET SDK" -in $Dependencies) {
                Write-ColorText "🔧 .NET 8 SDK:" "Cyan"
                Write-ColorText "   Download: https://dotnet.microsoft.com/download/dotnet/8.0" "Gray"
                Write-ColorText "   Or use: brew install --cask dotnet" "Gray"
                Write-Host ""
            }
        }
    }
}

function Invoke-PlatformScript {
    param([string]$ScriptName, [string]$Platform, [array]$Arguments = @())
    
    try {
        $scriptPath = Get-ScriptPath -ScriptName $ScriptName -Platform $Platform
        
        if (-not (Test-Path $scriptPath)) {
            Write-ColorText "❌ Script not found: $scriptPath" "Red"
            return $false
        }
        
        Write-ColorText "🚀 Launching $ScriptName for $Platform..." "Green"
        
        switch ($Platform) {
            "Windows" {
                & $scriptPath @Arguments
            }
            { $_ -in @("Linux", "macOS") } {
                & bash $scriptPath @Arguments
            }
        }
        
        return $true
    } catch {
        Write-ColorText "❌ Failed to execute script: $($_.Exception.Message)" "Red"
        return $false
    }
}

function Show-Help {
    Write-ColorText "🎯 Notify Service API - Development Setup" "Yellow"
    Write-Host ""
    Write-ColorText "USAGE:" "White"
    Write-ColorText "  .\scripts\dev-setup.ps1 [OPTIONS]" "Gray"
    Write-Host ""
    Write-ColorText "OPTIONS:" "White"
    Write-ColorText "  -Menu     Launch interactive development menu" "Gray"
    Write-ColorText "  -Start    Start development environment" "Gray"
    Write-ColorText "  -Stop     Stop development services" "Gray"
    Write-ColorText "  -Config   Open configuration manager" "Gray"
    Write-ColorText "  -Help     Show this help message" "Gray"
    Write-Host ""
    Write-ColorText "EXAMPLES:" "White"
    Write-ColorText "  .\scripts\dev-setup.ps1 -Menu     # Interactive menu" "Gray"
    Write-ColorText "  .\scripts\dev-setup.ps1 -Start    # Quick start" "Gray"
    Write-ColorText "  .\scripts\dev-setup.ps1 -Config   # Configure settings" "Gray"
    Write-Host ""
    Write-ColorText "PLATFORM SUPPORT:" "White"
    Write-ColorText "  ✅ Windows (PowerShell scripts)" "Green"
    Write-ColorText "  ✅ Linux (Bash scripts)" "Green"
    Write-ColorText "  ✅ macOS (Bash scripts)" "Green"
    Write-Host ""
}

# Main execution
try {
    $platform = Get-Platform
    
    if ($Help) {
        Show-Header
        Show-Help
        exit 0
    }
    
    Show-Header
    Write-ColorText "🖥️ Detected Platform: $platform" "Cyan"
    Write-Host ""
    
    # Check dependencies
    if (-not (Test-Dependencies -Platform $platform)) {
        Write-ColorText "❌ Please install missing dependencies and try again." "Red"
        exit 1
    }
    
    Write-Host ""
    
    # Execute based on parameters
    if ($Menu) {
        Invoke-PlatformScript -ScriptName "dev-menu" -Platform $platform
    } elseif ($Start) {
        Invoke-PlatformScript -ScriptName "start-dev-services" -Platform $platform
    } elseif ($Stop) {
        Invoke-PlatformScript -ScriptName "stop-dev-services" -Platform $platform
    } elseif ($Config) {
        Invoke-PlatformScript -ScriptName "config-manager" -Platform $platform
    } else {
        # Default to menu
        Write-ColorText "🎛️ No specific action specified, launching interactive menu..." "Yellow"
        Start-Sleep -Seconds 2
        Invoke-PlatformScript -ScriptName "dev-menu" -Platform $platform
    }
    
} catch {
    Write-ColorText "❌ An error occurred: $($_.Exception.Message)" "Red"
    exit 1
}
