using System;
using System.Threading;
using System.Threading.Tasks;

namespace Services.Interfaces;

public interface IMessageScheduler<TPayload, TScheduleResult>
{
    Task<TScheduleResult> ScheduleMessageAsync(TPayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default);
    Task<bool> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default);
}
