-- Create databases for Notify Service API
-- Run this script as postgres superuser

-- Create the main application database
CREATE DATABASE "NotifyDb"
    WITH 
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'English_United States.1252'
    LC_CTYPE = 'English_United States.1252'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- Create the identity database
CREATE DATABASE "NotifyIdentityDb"
    WITH 
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'English_United States.1252'
    LC_CTYPE = 'English_United States.1252'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- Grant privileges (if using a different user)
-- GRANT ALL PRIVILEGES ON DATABASE "NotifyDb" TO your_user;
-- GRANT ALL PRIVILEGES ON DATABASE "NotifyIdentityDb" TO your_user;
