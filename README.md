# Notify Service API

A comprehensive notification service API built with .NET 9.0, featuring PostgreSQL database integration, Redis caching, and comprehensive API documentation.

## Recent Updates

### Database Migration: MongoDB → PostgreSQL
- **Migrated from MongoDB to PostgreSQL** for better relational data management
- Converted `LoginLog` entity from MongoDB document to PostgreSQL entity
- Implemented Entity Framework Core repositories replacing MongoDB repositories
- Updated service layer to use PostgreSQL repositories
- Removed Data.Mongo project and all MongoDB dependencies

### Package Updates
- Updated all packages to stable .NET 9.0 versions
- Resolved package version conflicts
- Fixed preview package dependencies in Identity project
- Updated Entity Framework Core to 9.0.6
- Updated ASP.NET Core packages to 9.0.6

### Project Structure Improvements
- Added missing project references for better dependency management
- Added UnitTests project to solution file
- Enhanced repository pattern with specific interfaces
- Improved dependency injection configuration

### API Documentation
- Maintained Swagger/OpenAPI documentation
- Enhanced API documentation with better descriptions
- Ready for Scalar UI integration (pending compatibility updates)

## Architecture

### Libraries
- **Models**: Entity definitions and DTOs
- **Data**: Entity Framework Core context and repositories
- **Services**: Business logic and service implementations
- **Core**: Shared services and extensions
- **Identity**: Authentication and authorization
- **Caching**: Redis caching implementation

### Database
- **PostgreSQL**: Primary database for application data
- **Entity Framework Core**: ORM with code-first migrations
- **Connection String**: Configured for PostgreSQL in appsettings.json

### Caching
- **Redis**: Distributed caching for performance optimization
- **Health Checks**: Integrated health monitoring for Redis and PostgreSQL

## Getting Started

### Prerequisites
- .NET 9.0 SDK
- PostgreSQL database
- Redis server

### Configuration
Update `appsettings.json` with your database and Redis connection strings:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=ApplicationDb;Username=postgres;Password=yourpassword",
    "IdentityConnection": "Host=localhost;Database=IdentityDb;Username=postgres;Password=yourpassword"
  },
  "RedisSettings": {
    "RedisConnectionString": "localhost:6379"
  }
}
```

### Running the Application
```bash
dotnet restore
dotnet build
dotnet run --project Presentations/WebApi
```

### API Documentation
Access the Swagger UI at: `https://localhost:5001/swagger`

## Health Checks
Monitor application health at: `https://localhost:5001/health`

## Features
- User authentication and authorization
- Login logging with PostgreSQL storage
- Email notifications
- SMS messaging
- Push notifications
- GraphQL API endpoints
- Comprehensive health monitoring
- Redis caching for performance
- Structured logging with Serilog
