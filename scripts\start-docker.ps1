# ═══════════════════════════════════════════════════════════════════════════════
# 🐳 Docker Desktop Startup Helper
# ═══════════════════════════════════════════════════════════════════════════════
# Helps start Docker Desktop and wait for it to be ready

param(
    [switch]$Wait,
    [int]$TimeoutSeconds = 120
)

# Colors and functions
function Write-Step {
    param([string]$Message, [string]$Status = "INFO")
    $timestamp = Get-Date -Format "HH:mm:ss"
    switch ($Status) {
        "SUCCESS" { Write-Host "[$timestamp] ✅ $Message" -ForegroundColor Green }
        "ERROR"   { Write-Host "[$timestamp] ❌ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "[$timestamp] ⚠️  $Message" -ForegroundColor Yellow }
        "INFO"    { Write-Host "[$timestamp] 🔄 $Message" -ForegroundColor Cyan }
        "WAIT"    { Write-Host "[$timestamp] ⏳ $Message" -ForegroundColor Magenta }
    }
}

function Write-Section {
    param([string]$Title)
    Write-Host ""
    Write-Host "┌─────────────────────────────────────────────────────────────────────────────┐" -ForegroundColor DarkGray
    Write-Host "│ $($Title.PadRight(75)) │" -ForegroundColor White
    Write-Host "└─────────────────────────────────────────────────────────────────────────────┘" -ForegroundColor DarkGray
}

# Header
Write-Host ""
Write-Host "    🐳🐳🐳 DOCKER DESKTOP STARTUP HELPER 🐳🐳🐳" -ForegroundColor Cyan
Write-Host ""
Write-Host "    🚀 Starting Docker Desktop for Notify Service API" -ForegroundColor Yellow
Write-Host ""
Write-Host "═══════════════════════════════════════════════════════════════════════════════" -ForegroundColor DarkGray

Write-Section "🔍 Docker Status Check"

# Check if Docker command exists
try {
    $dockerPath = Get-Command docker -ErrorAction Stop
    Write-Step "Docker CLI found at: $($dockerPath.Source)" "SUCCESS"
} catch {
    Write-Step "Docker CLI not found in PATH" "ERROR"
    Write-Host "    📥 Please install Docker Desktop first" -ForegroundColor Yellow
    Write-Host "    🔗 Download from: https://www.docker.com/products/docker-desktop" -ForegroundColor Cyan
    exit 1
}

# Check if Docker Desktop is already running
Write-Step "Checking Docker daemon status..." "INFO"
try {
    docker info | Out-Null 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Step "Docker Desktop is already running!" "SUCCESS"
        Write-Host ""
        Write-Host "    🎉 Docker is ready to use!" -ForegroundColor Green
        Write-Host "    🚀 You can now run: .\scripts\start-dev-services.ps1" -ForegroundColor Cyan
        exit 0
    }
} catch {
    # Docker daemon not running
}

Write-Step "Docker daemon is not running" "WARNING"

Write-Section "🚀 Starting Docker Desktop"

# Find Docker Desktop executable
$dockerDesktopPaths = @(
    "${env:ProgramFiles}\Docker\Docker\Docker Desktop.exe",
    "${env:ProgramFiles(x86)}\Docker\Docker\Docker Desktop.exe",
    "${env:LOCALAPPDATA}\Programs\Docker\Docker\Docker Desktop.exe"
)

$dockerDesktopPath = $null
foreach ($path in $dockerDesktopPaths) {
    if (Test-Path $path) {
        $dockerDesktopPath = $path
        break
    }
}

if (-not $dockerDesktopPath) {
    Write-Step "Docker Desktop executable not found" "ERROR"
    Write-Host "    📍 Please start Docker Desktop manually from:" -ForegroundColor Yellow
    Write-Host "    • Start Menu → Docker Desktop" -ForegroundColor Gray
    Write-Host "    • System Tray → Docker icon" -ForegroundColor Gray
    Write-Host "    • Desktop shortcut" -ForegroundColor Gray
    exit 1
}

Write-Step "Found Docker Desktop at: $dockerDesktopPath" "SUCCESS"
Write-Step "Starting Docker Desktop..." "INFO"

try {
    Start-Process -FilePath $dockerDesktopPath -WindowStyle Hidden
    Write-Step "Docker Desktop startup initiated" "SUCCESS"
} catch {
    Write-Step "Failed to start Docker Desktop: $($_.Exception.Message)" "ERROR"
    exit 1
}

if ($Wait) {
    Write-Section "⏳ Waiting for Docker to be Ready"
    
    Write-Step "Waiting for Docker daemon to start (timeout: ${TimeoutSeconds}s)..." "WAIT"
    
    $startTime = Get-Date
    $ready = $false
    
    while ((Get-Date) -lt $startTime.AddSeconds($TimeoutSeconds)) {
        try {
            docker info | Out-Null 2>&1
            if ($LASTEXITCODE -eq 0) {
                $ready = $true
                break
            }
        } catch {
            # Still waiting
        }
        
        $elapsed = [math]::Round(((Get-Date) - $startTime).TotalSeconds)
        Write-Host "`r    ⏳ Waiting... ${elapsed}s elapsed" -NoNewline -ForegroundColor Magenta
        Start-Sleep -Seconds 2
    }
    
    Write-Host "" # New line after progress
    
    if ($ready) {
        Write-Step "Docker Desktop is now ready!" "SUCCESS"
        
        # Show Docker info
        Write-Section "📊 Docker Information"
        try {
            $dockerVersion = docker --version
            Write-Step "Version: $dockerVersion" "INFO"
            
            $dockerInfo = docker info --format "{{.ServerVersion}}"
            Write-Step "Server Version: $dockerInfo" "INFO"
            
            Write-Step "Docker daemon is healthy and ready" "SUCCESS"
        } catch {
            Write-Step "Docker is running but info retrieval failed" "WARNING"
        }
        
        Write-Section "🎉 Ready to Develop!"
        Write-Host ""
        Write-Host "    🚀 Docker Desktop is now running and ready!" -ForegroundColor Green
        Write-Host ""
        Write-Host "    📝 Next Steps:" -ForegroundColor White
        Write-Host "    ├─ Start development environment: " -NoNewline -ForegroundColor Gray
        Write-Host ".\scripts\start-dev-services.ps1" -ForegroundColor Green
        Write-Host "    ├─ Open development menu: " -NoNewline -ForegroundColor Gray
        Write-Host ".\scripts\dev-menu.ps1" -ForegroundColor Green
        Write-Host "    └─ Configure settings: " -NoNewline -ForegroundColor Gray
        Write-Host ".\scripts\config-manager.ps1" -ForegroundColor Green
        Write-Host ""
        
    } else {
        Write-Step "Docker Desktop failed to start within timeout" "ERROR"
        Write-Host ""
        Write-Host "    🔧 Troubleshooting:" -ForegroundColor Yellow
        Write-Host "    ├─ Check if Docker Desktop is starting in system tray" -ForegroundColor Gray
        Write-Host "    ├─ Try starting Docker Desktop manually" -ForegroundColor Gray
        Write-Host "    ├─ Restart your computer if Docker seems stuck" -ForegroundColor Gray
        Write-Host "    └─ Reinstall Docker Desktop if problems persist" -ForegroundColor Gray
        Write-Host ""
        exit 1
    fi
} else {
    Write-Section "🎯 Docker Desktop Starting"
    Write-Host ""
    Write-Host "    🚀 Docker Desktop is starting in the background..." -ForegroundColor Green
    Write-Host ""
    Write-Host "    📝 Next Steps:" -ForegroundColor White
    Write-Host "    ├─ Wait for Docker Desktop to fully start (1-2 minutes)" -ForegroundColor Gray
    Write-Host "    ├─ Check system tray for Docker icon" -ForegroundColor Gray
    Write-Host "    └─ Run this script with -Wait to monitor startup:" -ForegroundColor Gray
    Write-Host "      " -NoNewline
    Write-Host ".\scripts\start-docker.ps1 -Wait" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "    🔄 Or run the development environment (it will wait for Docker):" -ForegroundColor White
    Write-Host "      " -NoNewline
    Write-Host ".\scripts\start-dev-services.ps1" -ForegroundColor Green
    Write-Host ""
}

Write-Host "═══════════════════════════════════════════════════════════════════════════════" -ForegroundColor DarkGray
