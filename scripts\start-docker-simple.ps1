# ═══════════════════════════════════════════════════════════════════════════════
# 🐳 Simple Docker Desktop Startup Helper
# ═══════════════════════════════════════════════════════════════════════════════
# Simplified version that uses Windows Start command

param(
    [int]$TimeoutSeconds = 120
)

function Write-Step {
    param([string]$Message, [string]$Status = "INFO")
    $timestamp = Get-Date -Format "HH:mm:ss"
    switch ($Status) {
        "SUCCESS" { Write-Host "[$timestamp] ✅ $Message" -ForegroundColor Green }
        "ERROR"   { Write-Host "[$timestamp] ❌ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "[$timestamp] ⚠️  $Message" -ForegroundColor Yellow }
        "INFO"    { Write-Host "[$timestamp] 🔄 $Message" -ForegroundColor Cyan }
        "WAIT"    { Write-Host "[$timestamp] ⏳ $Message" -ForegroundColor Magenta }
    }
}

Write-Host ""
Write-Host "🐳 Starting Docker Desktop..." -ForegroundColor Cyan
Write-Host ""

# Check if Docker is already running
Write-Step "Checking Docker daemon status..." "INFO"
try {
    docker info | Out-Null 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Step "Docker Desktop is already running!" "SUCCESS"
        exit 0
    }
} catch {
    # Docker daemon not running
}

Write-Step "Docker daemon is not running" "WARNING"

# Try to start Docker Desktop using Windows Start command
Write-Step "Attempting to start Docker Desktop..." "INFO"
try {
    # Use Start-Process with Docker Desktop from Start Menu
    Start-Process -FilePath "cmd.exe" -ArgumentList "/c", "start", "Docker Desktop" -WindowStyle Hidden -ErrorAction Stop
    Write-Step "Docker Desktop startup command sent" "SUCCESS"
} catch {
    Write-Step "Failed to start Docker Desktop automatically" "ERROR"
    Write-Host ""
    Write-Host "    🔧 Please start Docker Desktop manually:" -ForegroundColor Yellow
    Write-Host "    1. Press Windows key" -ForegroundColor Gray
    Write-Host "    2. Type 'Docker Desktop'" -ForegroundColor Gray
    Write-Host "    3. Click on Docker Desktop" -ForegroundColor Gray
    Write-Host "    4. Wait for it to start (check system tray)" -ForegroundColor Gray
    Write-Host ""
    exit 1
}

# Wait for Docker to be ready
Write-Step "Waiting for Docker daemon to start (timeout: ${TimeoutSeconds}s)..." "WAIT"

$startTime = Get-Date
$ready = $false

while ((Get-Date) -lt $startTime.AddSeconds($TimeoutSeconds)) {
    try {
        docker info | Out-Null 2>&1
        if ($LASTEXITCODE -eq 0) {
            $ready = $true
            break
        }
    } catch {
        # Still waiting
    }
    
    $elapsed = [math]::Round(((Get-Date) - $startTime).TotalSeconds)
    Write-Host "`r    ⏳ Waiting for Docker... ${elapsed}s elapsed" -NoNewline -ForegroundColor Magenta
    Start-Sleep -Seconds 3
}

Write-Host "" # New line after progress

if ($ready) {
    Write-Step "Docker Desktop is now ready!" "SUCCESS"
    
    # Show Docker info
    try {
        $dockerVersion = docker --version
        Write-Step "Version: $dockerVersion" "INFO"
    } catch {
        Write-Step "Docker is running but version check failed" "WARNING"
    }
    
    Write-Host ""
    Write-Host "    🎉 Docker Desktop is ready!" -ForegroundColor Green
    Write-Host "    🚀 You can now continue with your development setup" -ForegroundColor Cyan
    Write-Host ""
    
} else {
    Write-Step "Docker Desktop failed to start within timeout" "ERROR"
    Write-Host ""
    Write-Host "    🔧 Troubleshooting:" -ForegroundColor Yellow
    Write-Host "    ├─ Check if Docker Desktop is starting (system tray icon)" -ForegroundColor Gray
    Write-Host "    ├─ Try starting Docker Desktop manually from Start Menu" -ForegroundColor Gray
    Write-Host "    ├─ Restart your computer if Docker seems stuck" -ForegroundColor Gray
    Write-Host "    └─ Reinstall Docker Desktop if problems persist" -ForegroundColor Gray
    Write-Host ""
    Write-Host "    📥 Download Docker Desktop: https://www.docker.com/products/docker-desktop" -ForegroundColor Cyan
    Write-Host ""
    exit 1
}
