﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.OpenApi.Models;
using Models.DTOs.SMS;
using Models.DTOs.WhatsApp;
using Services.Concrete;
using Services.Interfaces;
using System.IO;
using Data.Repos;

namespace Core;

public static class ServiceExtensions
{
    public static void AddSharedServices(this IServiceCollection services, IConfiguration config)
    {
        // Configuration
        services.Configure<MailSettings>(config.GetSection("MailSettings"));

        // Core services
        services.AddTransient<IEmailService, EmailService>();
        services.AddTransient<BulkSmsService>();
    }

    public static void AddApplicationSqlServer(this IServiceCollection services, IConfiguration config)
    {
        services.AddDbContext<ApplicationDbContext>(options =>
        {
            options.UseSqlServer(
                config.GetConnectionString("DefaultConnection"),
                b => b.MigrationsAssembly(typeof(ApplicationDbContext).Assembly.FullName)
            );
        });

        services.AddTransient<IDbContext, ApplicationDbContext>();

        // Ensure database creation
        using var context = services.BuildServiceProvider().GetService<ApplicationDbContext>();
        context?.Database.EnsureCreated();
    }

    public static void AddRepoServices(this IServiceCollection services, IConfiguration config)
    {
        services.AddTransient(typeof(IGenericRepository<>), typeof(GenericRepository<>));
        services.AddScoped<IUnitOfWork, UnitOfWork>();
        services.AddScoped<ILoginLogRepository, LoginLogRepository>();
    }

    public static void AddAppServices(this IServiceCollection services, IConfiguration config)
    {
        services.AddTransient<ILoginLogService, LoginLogService>();
    }

    public static void AddCustomSwagger(this IServiceCollection services, IConfiguration config)
    {
        services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new OpenApiInfo
            {
                Title = "Service API",
                Version = "v1",
                Description = "A comprehensive API for the notification service with Scalar UI documentation"
            });

            c.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, "WebApi.xml"));

            c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                Description = @"JWT Authorization header using the Bearer scheme.<br><br> 
                                Enter 'Bearer' [space] and then your token in the text input below.
                                <br><br>Example: 'Bearer 12345abcdef'",
                Name = "Authorization",
                In = ParameterLocation.Header,
                Type = SecuritySchemeType.ApiKey,
                Scheme = "Bearer"
            });

            c.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        },
                        Scheme = "oauth2",
                        Name = "Bearer",
                        In = ParameterLocation.Header
                    },
                    new List<string>()
                }
            });
        });
    }

    public static void AddAdditionalServices(this IServiceCollection services, IConfiguration config)
    {
        services.AddHttpClient<PushNotificationService>();
    }
}
