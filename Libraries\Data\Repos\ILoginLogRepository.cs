using Models.DbEntities;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Data.Repos;

public interface ILoginLogRepository : IGenericRepository<LoginLog>
{
    /// <summary>
    /// Get login logs by user email
    /// </summary>
    /// <param name="email">User email</param>
    /// <returns>List of login logs for the user</returns>
    Task<List<LoginLog>> GetByUserEmailAsync(string email);
    
    /// <summary>
    /// Add a new login log entry
    /// </summary>
    /// <param name="loginLog">Login log to add</param>
    /// <returns>Task</returns>
    Task AddAsync(LoginLog loginLog);
    
    /// <summary>
    /// Check if any login logs exist for a user
    /// </summary>
    /// <param name="email">User email</param>
    /// <returns>True if logs exist</returns>
    Task<bool> ExistsForUserAsync(string email);
}
