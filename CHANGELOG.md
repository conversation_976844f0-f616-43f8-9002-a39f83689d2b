# Changelog

All notable changes to the Notify Service API project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [2.0.0] - 2024-12-19

### 🚀 Major Changes

#### Database Migration: MongoDB → PostgreSQL
- **BREAKING**: Migrated from MongoDB to PostgreSQL for better relational data management
- **BREAKING**: Removed `Data.Mongo` project and all MongoDB dependencies
- **NEW**: Implemented Entity Framework Core repositories replacing MongoDB repositories
- **NEW**: Added PostgreSQL-specific `LoginLog` entity with proper constraints
- **NEW**: Created `ILoginLogRepository` interface for type-safe data access
- **UPDATED**: Modified `LoginLogService` to use PostgreSQL repositories

#### Framework Upgrade: .NET 9 → .NET 8
- **BREAKING**: Downgraded from .NET 9 to .NET 8 for better stability and ecosystem support
- **UPDATED**: All projects now target .NET 8.0 (except Models and Caching which use .NET Standard 2.0)
- **UPDATED**: All NuGet packages updated to .NET 8 compatible versions

#### API Documentation: Swagger → Scalar UI
- **NEW**: Implemented Scalar UI for modern, interactive API documentation
- **ENHANCED**: Beautiful, responsive API reference interface
- **IMPROVED**: Better code samples and authentication testing capabilities
- **MAINTAINED**: Backward compatibility with OpenAPI/Swagger specifications

### 📦 Package Updates

#### Core Dependencies
- `Microsoft.EntityFrameworkCore.*`: 9.0.6 → 8.0.8
- `Microsoft.AspNetCore.*`: 9.0.6 → 8.0.8
- `Npgsql.EntityFrameworkCore.PostgreSQL`: 9.0.4 → 8.0.4

#### Development Dependencies
- `Swashbuckle.AspNetCore`: 7.2.0 → 6.7.3
- `Serilog.AspNetCore`: 9.0.0 → 8.0.2
- `AspNetCore.HealthChecks.*`: 9.0.0 → 8.0.x

#### New Packages
- `Scalar.AspNetCore`: 1.2.42 (NEW)
- `System.ComponentModel.Annotations`: 5.0.0 (NEW for Models project)
- `Microsoft.Extensions.Configuration.Binder`: 8.0.0 (NEW for Caching project)

### 🏗️ Architecture Improvements

#### Project Structure
- **ENHANCED**: Models project now uses .NET Standard 2.0 with C# 10 language features
- **ENHANCED**: Caching project converted to .NET Standard 2.0 for better portability
- **IMPROVED**: Better separation of concerns with cleaner dependency management
- **ADDED**: UnitTests project properly integrated into solution

#### Repository Pattern
- **NEW**: Generic repository pattern with Entity Framework Core
- **NEW**: Unit of Work pattern for transaction management
- **NEW**: Specific repository interfaces for type safety
- **IMPROVED**: Better abstraction between data access and business logic

#### Configuration Management
- **REMOVED**: MongoDB configuration sections
- **ENHANCED**: PostgreSQL connection string management
- **IMPROVED**: Redis configuration with proper binding
- **STANDARDIZED**: Consistent configuration patterns across projects

### 🔧 Technical Improvements

#### Code Quality
- **FIXED**: Resolved all package version conflicts
- **FIXED**: Updated preview packages to stable releases
- **IMPROVED**: Better nullable reference type handling
- **ENHANCED**: Consistent coding standards across projects

#### Performance
- **OPTIMIZED**: Entity Framework Core queries with proper indexing
- **IMPROVED**: Redis caching with better serialization
- **ENHANCED**: Connection pooling for database operations

#### Security
- **MAINTAINED**: JWT authentication and authorization
- **IMPROVED**: Better password hashing with updated libraries
- **ENHANCED**: Secure configuration management

### 🐛 Bug Fixes
- Fixed compilation errors in Services project
- Resolved interface implementation mismatches
- Fixed nullable reference warnings in Identity project
- Corrected package dependency conflicts
- Fixed configuration binding issues in Caching project

### 📚 Documentation
- **NEW**: Comprehensive README with setup instructions
- **NEW**: Architecture diagrams and project structure documentation
- **NEW**: API documentation with Scalar UI
- **ENHANCED**: Code comments and XML documentation
- **ADDED**: Migration guide for MongoDB to PostgreSQL

### 🔄 Migration Guide

#### For Developers
1. **Database**: Update connection strings to PostgreSQL format
2. **Dependencies**: Run `dotnet restore` to get updated packages
3. **Configuration**: Remove MongoDB settings from appsettings.json
4. **Code**: Update any direct MongoDB references to use new repositories

#### Breaking Changes
- MongoDB support completely removed
- Some package APIs may have changed with .NET 8 downgrade
- Configuration section names may have changed

### 🚀 What's Next
- Enhanced authentication flows
- Additional notification channels
- Performance monitoring improvements
- Automated testing pipeline
- Docker containerization updates

---

## [1.0.0] - Previous Version

### Initial Release
- MongoDB-based data storage
- .NET 9 framework
- Basic notification services (Email, SMS, Push)
- Swagger UI documentation
- Redis caching
- JWT authentication
- Health monitoring

---

## Legend
- 🚀 **Major Changes**: Significant new features or breaking changes
- 📦 **Package Updates**: Dependency and package version changes
- 🏗️ **Architecture**: Structural and design improvements
- 🔧 **Technical**: Code quality and performance improvements
- 🐛 **Bug Fixes**: Resolved issues and fixes
- 📚 **Documentation**: Documentation updates and additions
- 🔄 **Migration**: Migration guides and breaking changes
