{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "Seq": {"Url": "http://localhost:5341"}}, "ConnectionStrings": {"DefaultConnection": "Host=********-db;Database=ApplicationDb;Username=********;Password=********", "IdentityConnection": "Host=********-db;Database=IdentityDb;Username=********;Password=*********"}, "JWTSettings": {"Key": "sinan_tok_graphql_webapi_identiy13", "Issuer": "CoreIdentity", "Audience": "CoreIdentityUser", "DurationInMinutes": 130}, "MailSettings": {"EmailFrom": "<EMAIL>", "SmtpHost": "smtp.ethereal.email", "SmtpPort": 587, "SmtpUser": "<EMAIL>", "SmtpPass": "rcveN5f77wZjgxNx5v", "DisplayName": "<PERSON><PERSON>"}, "RedisSettings": {"RedisDataProtectionKey": "", "CacheTime": "5", "RedisConnectionString": "redis:6379", "RedisDatabaseId": ""}, "AllowedHosts": "*"}