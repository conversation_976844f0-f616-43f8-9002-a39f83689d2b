{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "Seq": {"Url": "http://localhost:5341"}}, "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=NotifyDb;Username=********;Password=********", "IdentityConnection": "Host=localhost;Database=NotifyIdentityDb;Username=********;Password=********"}, "JWTSettings": {"Key": "sinan_tok_graphql_webapi_identiy13", "Issuer": "CoreIdentity", "Audience": "CoreIdentityUser", "DurationInMinutes": 130}, "MailSettings": {"EmailFrom": "<EMAIL>", "SmtpHost": "smtp.ethereal.email", "SmtpPort": 587, "SmtpUser": "<EMAIL>", "SmtpPass": "rcveN5f77wZjgxNx5v", "DisplayName": "<PERSON><PERSON>"}, "RedisSettings": {"RedisDataProtectionKey": "", "CacheTime": "5", "RedisConnectionString": "localhost:6379", "RedisDatabaseId": "0"}, "AllowedHosts": "*"}