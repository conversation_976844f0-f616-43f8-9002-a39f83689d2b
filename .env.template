# =========================
# Environment Variables Template
# Copy this file to .env and update with your values
# =========================

# =========================
# Application Configuration
# =========================
ASPNETCORE_ENVIRONMENT=Production
API_PORT=8080

# =========================
# Database Configuration
# =========================
POSTGRES_PASSWORD=your_secure_postgres_password_here
POSTGRES_PORT=5432

# =========================
# Redis Configuration
# =========================
REDIS_PASSWORD=your_redis_password_here
REDIS_PORT=6379

# =========================
# JWT Configuration
# =========================
JWT_SECRET_KEY=your-super-secret-jwt-key-here-minimum-32-characters-long

# =========================
# Email Configuration
# =========================
MAIL_FROM=<EMAIL>
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
MAIL_DISPLAY_NAME=Notify Service

# =========================
# NGINX Configuration
# =========================
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443

# =========================
# Azure Configuration (for CI/CD)
# =========================
AZURE_SUBSCRIPTION_ID=your-azure-subscription-id
AZURE_RESOURCE_GROUP=notify-service-rg
AZURE_WEBAPP_NAME=notify-service-api
AZURE_CONTAINER_REGISTRY=notifyregistry.azurecr.io

# =========================
# Monitoring Configuration
# =========================
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# =========================
# Development Only
# =========================
# Uncomment for development
# ASPNETCORE_ENVIRONMENT=Development
# API_PORT=5000
# POSTGRES_PASSWORD=devpassword123
