# 🏗️ Notify Service API - Architecture Documentation

This document provides detailed architectural diagrams and explanations for the Notify Service API.

## 📋 Table of Contents

- [System Overview](#system-overview)
- [Project Structure](#project-structure)
- [Data Flow Architecture](#data-flow-architecture)
- [Database Schema](#database-schema)
- [Authentication Flow](#authentication-flow)
- [Notification Pipeline](#notification-pipeline)
- [Caching Strategy](#caching-strategy)
- [Deployment Architecture](#deployment-architecture)

## 🌐 System Overview

```mermaid
graph TB
    subgraph "External Clients"
        WEB[Web Applications]
        MOB[Mobile Apps]
        API_CLIENT[API Clients]
    end

    subgraph "Load Balancer"
        LB[Load Balancer/Reverse Proxy]
    end

    subgraph "Notify Service API"
        WEBAPI[WebApi Layer]
        GRAPHQL[GraphQL Endpoint]
        SCALAR[Scalar UI Documentation]
    end

    subgraph "Business Logic"
        SERVICES[Services Layer]
        CORE[Core Services]
        IDENTITY[Identity Services]
    end

    subgraph "Data Access"
        REPOS[Repository Layer]
        UOW[Unit of Work]
        EF[Entity Framework Core]
    end

    subgraph "External Services"
        EMAIL[Email Provider]
        SMS[SMS Gateway]
        PUSH[Push Notification Service]
    end

    subgraph "Data Storage"
        POSTGRES[(PostgreSQL Database)]
        REDIS[(Redis Cache)]
    end

    subgraph "Monitoring"
        HEALTH[Health Checks]
        LOGS[Structured Logging]
        METRICS[Performance Metrics]
    end

    WEB --> LB
    MOB --> LB
    API_CLIENT --> LB
    
    LB --> WEBAPI
    LB --> GRAPHQL
    LB --> SCALAR

    WEBAPI --> SERVICES
    GRAPHQL --> SERVICES
    SERVICES --> CORE
    SERVICES --> IDENTITY
    
    CORE --> REPOS
    IDENTITY --> REPOS
    REPOS --> UOW
    UOW --> EF
    EF --> POSTGRES

    SERVICES --> EMAIL
    SERVICES --> SMS
    SERVICES --> PUSH

    SERVICES --> REDIS
    REPOS --> REDIS

    WEBAPI --> HEALTH
    SERVICES --> LOGS
    REPOS --> METRICS
```

## 📁 Project Structure

```mermaid
graph TD
    subgraph "Solution: Notify.sln"
        subgraph "Presentations"
            WEBAPI_PROJ[WebApi.csproj<br/>🎯 .NET 8.0<br/>REST API & GraphQL]
        end

        subgraph "Libraries"
            MODELS[Models.csproj<br/>📦 .NET Standard 2.0<br/>Entities & DTOs]
            DATA[Data.csproj<br/>🎯 .NET 8.0<br/>EF Core & Repositories]
            SERVICES[Services.csproj<br/>🎯 .NET 8.0<br/>Business Logic]
            CORE[Core.csproj<br/>🎯 .NET 8.0<br/>Shared Services]
            IDENTITY[Identity.csproj<br/>🎯 .NET 8.0<br/>Authentication]
            CACHING[Caching.csproj<br/>📦 .NET Standard 2.0<br/>Redis Implementation]
        end

        subgraph "Tests"
            UNIT_TESTS[UnitTests.csproj<br/>🎯 .NET 8.0<br/>Unit & Integration Tests]
        end
    end

    WEBAPI_PROJ --> SERVICES
    WEBAPI_PROJ --> CORE
    WEBAPI_PROJ --> IDENTITY
    WEBAPI_PROJ --> CACHING
    WEBAPI_PROJ --> MODELS

    SERVICES --> DATA
    SERVICES --> MODELS
    
    CORE --> DATA
    CORE --> MODELS
    CORE --> SERVICES

    IDENTITY --> CORE
    IDENTITY --> MODELS

    DATA --> MODELS

    CACHING --> MODELS

    UNIT_TESTS --> WEBAPI_PROJ
    UNIT_TESTS --> SERVICES
    UNIT_TESTS --> DATA
    UNIT_TESTS --> CORE
    UNIT_TESTS --> IDENTITY
```

## 🔄 Data Flow Architecture

```mermaid
sequenceDiagram
    participant Client
    participant WebApi
    participant Auth as Authentication
    participant Service as Business Service
    participant Repo as Repository
    participant Cache as Redis Cache
    participant DB as PostgreSQL
    participant External as External Service

    Client->>WebApi: HTTP Request
    WebApi->>Auth: Validate JWT Token
    Auth-->>WebApi: User Claims
    
    WebApi->>Service: Business Logic Call
    Service->>Cache: Check Cache
    
    alt Cache Hit
        Cache-->>Service: Cached Data
    else Cache Miss
        Service->>Repo: Data Request
        Repo->>DB: SQL Query
        DB-->>Repo: Result Set
        Repo-->>Service: Domain Objects
        Service->>Cache: Store in Cache
    end

    alt Notification Required
        Service->>External: Send Notification
        External-->>Service: Delivery Status
        Service->>Repo: Log Activity
        Repo->>DB: Insert Log
    end

    Service-->>WebApi: Response Data
    WebApi-->>Client: HTTP Response

## 🗄️ Database Schema

```mermaid
erDiagram
    ApplicationUser {
        string Id PK
        string FirstName
        string LastName
        string Email
        string UserName
        string PasswordHash
        bool EmailConfirmed
        datetime CreatedDate
        datetime LastLoginDate
    }

    ApplicationRole {
        string Id PK
        string Name
        string NormalizedName
        string Description
    }

    ApplicationUserRole {
        string UserId PK,FK
        string RoleId PK,FK
    }

    LoginLog {
        int Id PK
        string UserEmail
        datetime LoginTime
        datetime CreateUTC
    }

    Note {
        int Id PK
        string Title
        string Content
        datetime CreateUTC
        string UserId FK
    }

    RefreshToken {
        int Id PK
        string Token
        datetime Expires
        bool IsExpired
        datetime Created
        string CreatedByIp
        datetime Revoked
        string RevokedByIp
        string ReplacedByToken
        string UserId FK
    }

    ApplicationUser ||--o{ ApplicationUserRole : "has roles"
    ApplicationRole ||--o{ ApplicationUserRole : "assigned to users"
    ApplicationUser ||--o{ Note : "creates"
    ApplicationUser ||--o{ RefreshToken : "owns"
    ApplicationUser ||--o{ LoginLog : "generates"
```

## 🔐 Authentication Flow

```mermaid
sequenceDiagram
    participant Client
    participant WebApi
    participant AuthService
    participant UserManager
    participant TokenService
    participant DB as PostgreSQL
    participant Cache as Redis

    Client->>WebApi: POST /api/account/authenticate
    WebApi->>AuthService: AuthenticateAsync(request)

    AuthService->>UserManager: FindByEmailAsync(email)
    UserManager->>DB: Query Users Table
    DB-->>UserManager: User Data

    AuthService->>UserManager: CheckPasswordAsync(user, password)
    UserManager-->>AuthService: Password Valid

    AuthService->>TokenService: GenerateJwtToken(user)
    TokenService->>DB: Get User Roles
    DB-->>TokenService: Role Claims
    TokenService-->>AuthService: JWT Token

    AuthService->>TokenService: GenerateRefreshToken()
    TokenService-->>AuthService: Refresh Token

    AuthService->>DB: Save Refresh Token
    AuthService->>DB: Log Login Activity

    AuthService->>Cache: Cache User Session

    AuthService-->>WebApi: Authentication Response
    WebApi-->>Client: JWT + Refresh Token

    Note over Client: Store tokens securely

    Client->>WebApi: API Request with JWT
    WebApi->>WebApi: Validate JWT
    WebApi->>Cache: Check User Session
    Cache-->>WebApi: Session Valid
    WebApi->>WebApi: Process Request
    WebApi-->>Client: API Response
```

## 📬 Notification Pipeline

```mermaid
graph TD
    subgraph "Notification Request"
        REQ[Notification Request]
        VALIDATE[Validate Request]
        ROUTE[Route by Type]
    end

    subgraph "Email Pipeline"
        EMAIL_PREP[Prepare Email]
        EMAIL_TEMPLATE[Apply Template]
        EMAIL_SEND[Send via SMTP]
        EMAIL_LOG[Log Email Activity]
    end

    subgraph "SMS Pipeline"
        SMS_PREP[Prepare SMS]
        SMS_VALIDATE[Validate Phone]
        SMS_SEND[Send via Gateway]
        SMS_LOG[Log SMS Activity]
    end

    subgraph "Push Pipeline"
        PUSH_PREP[Prepare Push]
        PUSH_TARGET[Target Devices]
        PUSH_SEND[Send via FCM/APNS]
        PUSH_LOG[Log Push Activity]
    end

    subgraph "Delivery Tracking"
        TRACK[Track Delivery]
        STATUS[Update Status]
        RETRY[Retry Logic]
        REPORT[Generate Reports]
    end

    subgraph "Storage & Caching"
        CACHE_CHECK{Cache Check}
        CACHE_STORE[Store in Cache]
        DB_LOG[Database Logging]
        METRICS[Update Metrics]
    end

    REQ --> VALIDATE
    VALIDATE --> ROUTE

    ROUTE -->|Email| EMAIL_PREP
    ROUTE -->|SMS| SMS_PREP
    ROUTE -->|Push| PUSH_PREP

    EMAIL_PREP --> EMAIL_TEMPLATE
    EMAIL_TEMPLATE --> EMAIL_SEND
    EMAIL_SEND --> EMAIL_LOG

    SMS_PREP --> SMS_VALIDATE
    SMS_VALIDATE --> SMS_SEND
    SMS_SEND --> SMS_LOG

    PUSH_PREP --> PUSH_TARGET
    PUSH_TARGET --> PUSH_SEND
    PUSH_SEND --> PUSH_LOG

    EMAIL_LOG --> TRACK
    SMS_LOG --> TRACK
    PUSH_LOG --> TRACK

    TRACK --> STATUS
    STATUS --> RETRY
    RETRY --> REPORT

    VALIDATE --> CACHE_CHECK
    CACHE_CHECK -->|Hit| CACHE_STORE
    CACHE_CHECK -->|Miss| DB_LOG
    DB_LOG --> CACHE_STORE
    CACHE_STORE --> METRICS

## 🚀 Caching Strategy

```mermaid
graph TD
    subgraph "Application Layer"
        APP[Application Request]
        CACHE_ATTR[CachedAttribute]
    end

    subgraph "Cache Manager"
        REDIS_MGR[Redis Cache Manager]
        CACHE_KEY[Generate Cache Key]
        SERIALIZE[Serialize/Deserialize]
    end

    subgraph "Redis Cluster"
        REDIS_PRIMARY[(Redis Primary)]
        REDIS_REPLICA[(Redis Replica)]
    end

    subgraph "Fallback"
        DATABASE[(PostgreSQL)]
        MEMORY_CACHE[In-Memory Cache]
    end

    subgraph "Cache Policies"
        TTL[Time-To-Live]
        EVICTION[LRU Eviction]
        INVALIDATION[Cache Invalidation]
    end

    APP --> CACHE_ATTR
    CACHE_ATTR --> REDIS_MGR
    REDIS_MGR --> CACHE_KEY
    CACHE_KEY --> SERIALIZE

    SERIALIZE -->|Cache Hit| REDIS_PRIMARY
    SERIALIZE -->|Cache Miss| DATABASE

    REDIS_PRIMARY --> REDIS_REPLICA
    DATABASE --> REDIS_MGR

    REDIS_MGR --> TTL
    TTL --> EVICTION
    EVICTION --> INVALIDATION

    REDIS_PRIMARY -->|Failure| MEMORY_CACHE
    MEMORY_CACHE -->|Fallback| DATABASE
```

## 🌐 Deployment Architecture

```mermaid
graph TB
    subgraph "Internet"
        USERS[Users/Clients]
        CDN[Content Delivery Network]
    end

    subgraph "Load Balancer Tier"
        LB[Load Balancer<br/>NGINX/HAProxy]
        SSL[SSL Termination]
    end

    subgraph "Application Tier"
        API1[Notify API Instance 1<br/>Docker Container]
        API2[Notify API Instance 2<br/>Docker Container]
        API3[Notify API Instance N<br/>Docker Container]
    end

    subgraph "Service Mesh"
        CONSUL[Service Discovery<br/>Consul]
        ENVOY[Envoy Proxy]
    end

    subgraph "Data Tier"
        PG_PRIMARY[(PostgreSQL Primary)]
        PG_REPLICA[(PostgreSQL Replica)]
        REDIS_CLUSTER[(Redis Cluster)]
    end

    subgraph "External Services"
        SMTP[SMTP Server]
        SMS_GW[SMS Gateway]
        PUSH_SVC[Push Service]
    end

    subgraph "Monitoring & Logging"
        PROMETHEUS[Prometheus]
        GRAFANA[Grafana]
        ELK[ELK Stack]
        JAEGER[Jaeger Tracing]
    end

    subgraph "Infrastructure"
        K8S[Kubernetes Cluster]
        DOCKER[Docker Registry]
        VAULT[HashiCorp Vault]
    end

    USERS --> CDN
    CDN --> LB
    LB --> SSL
    SSL --> ENVOY

    ENVOY --> API1
    ENVOY --> API2
    ENVOY --> API3

    API1 --> CONSUL
    API2 --> CONSUL
    API3 --> CONSUL

    API1 --> PG_PRIMARY
    API2 --> PG_PRIMARY
    API3 --> PG_PRIMARY

    PG_PRIMARY --> PG_REPLICA

    API1 --> REDIS_CLUSTER
    API2 --> REDIS_CLUSTER
    API3 --> REDIS_CLUSTER

    API1 --> SMTP
    API1 --> SMS_GW
    API1 --> PUSH_SVC

    API1 --> PROMETHEUS
    API2 --> PROMETHEUS
    API3 --> PROMETHEUS

    PROMETHEUS --> GRAFANA
    API1 --> ELK
    API1 --> JAEGER

    K8S --> API1
    K8S --> API2
    K8S --> API3
    K8S --> DOCKER
    K8S --> VAULT
```

## 📊 Performance Considerations

### Scalability Patterns
- **Horizontal Scaling**: Multiple API instances behind load balancer
- **Database Read Replicas**: Separate read/write operations
- **Redis Clustering**: Distributed caching for high availability
- **Async Processing**: Background jobs for heavy operations

### Optimization Strategies
- **Connection Pooling**: Efficient database connections
- **Query Optimization**: Indexed queries and proper EF Core usage
- **Caching Layers**: Multi-level caching (Redis + In-Memory)
- **Compression**: Response compression for large payloads

### Monitoring Points
- **API Response Times**: Track endpoint performance
- **Database Query Performance**: Monitor slow queries
- **Cache Hit Ratios**: Optimize caching effectiveness
- **External Service Latency**: Monitor third-party dependencies

## 🔒 Security Architecture

### Authentication & Authorization
- **JWT Tokens**: Stateless authentication
- **Role-Based Access Control**: Granular permissions
- **Refresh Token Rotation**: Enhanced security
- **Rate Limiting**: Prevent abuse

### Data Protection
- **Encryption at Rest**: Database encryption
- **Encryption in Transit**: TLS/SSL everywhere
- **Secrets Management**: HashiCorp Vault integration
- **Input Validation**: Comprehensive request validation

### Network Security
- **API Gateway**: Centralized security policies
- **CORS Configuration**: Controlled cross-origin access
- **IP Whitelisting**: Restricted access patterns
- **DDoS Protection**: Rate limiting and throttling
```
```
