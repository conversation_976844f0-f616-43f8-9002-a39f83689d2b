# ═══════════════════════════════════════════════════════════════════════════════
# ⚡ Notify Service API - Quick Start Script
# ═══════════════════════════════════════════════════════════════════════════════
# One-command setup for the entire development environment

param(
    [switch]$SkipDocker,
    [switch]$SkipConfig,
    [switch]$Force
)

# Colors and functions
function Write-Step {
    param([string]$Message, [string]$Status = "INFO")
    $timestamp = Get-Date -Format "HH:mm:ss"
    switch ($Status) {
        "SUCCESS" { Write-Host "[$timestamp] ✅ $Message" -ForegroundColor Green }
        "ERROR"   { Write-Host "[$timestamp] ❌ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "[$timestamp] ⚠️  $Message" -ForegroundColor Yellow }
        "INFO"    { Write-Host "[$timestamp] 🔄 $Message" -ForegroundColor Cyan }
        "WAIT"    { Write-Host "[$timestamp] ⏳ $Message" -ForegroundColor Magenta }
    }
}

function Write-Section {
    param([string]$Title)
    Write-Host ""
    Write-Host "┌─────────────────────────────────────────────────────────────────────────────┐" -ForegroundColor DarkGray
    Write-Host "│ $($Title.PadRight(75)) │" -ForegroundColor White
    Write-Host "└─────────────────────────────────────────────────────────────────────────────┘" -ForegroundColor DarkGray
}

function Show-DockerManualInstructions {
    Write-Host ""
    Write-Host "    🐳 Please start Docker Desktop manually:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "    📋 Step-by-step instructions:" -ForegroundColor White
    Write-Host "    1. Press Windows key (⊞)" -ForegroundColor Gray
    Write-Host "    2. Type 'Docker Desktop'" -ForegroundColor Gray
    Write-Host "    3. Click on 'Docker Desktop' when it appears" -ForegroundColor Gray
    Write-Host "    4. Wait for Docker Desktop to start (1-2 minutes)" -ForegroundColor Gray
    Write-Host "    5. Look for Docker whale icon in system tray" -ForegroundColor Gray
    Write-Host "    6. When ready, run this script again" -ForegroundColor Gray
    Write-Host ""
    Write-Host "    💡 Alternative methods:" -ForegroundColor White
    Write-Host "    • Double-click Docker Desktop icon on desktop" -ForegroundColor Gray
    Write-Host "    • Right-click Docker icon in system tray → 'Start'" -ForegroundColor Gray
    Write-Host ""
    Write-Host "    📥 If Docker Desktop is not installed:" -ForegroundColor White
    Write-Host "    └─ Download from: https://www.docker.com/products/docker-desktop" -ForegroundColor Cyan
    Write-Host ""
}

# Header
Clear-Host
Write-Host ""
Write-Host "    ⚡⚡⚡ NOTIFY SERVICE API - QUICK START ⚡⚡⚡" -ForegroundColor Yellow
Write-Host ""
Write-Host "    🚀 Complete Development Environment Setup" -ForegroundColor Cyan
Write-Host "    🎯 From Zero to Running API in Minutes" -ForegroundColor Gray
Write-Host ""
Write-Host "═══════════════════════════════════════════════════════════════════════════════" -ForegroundColor DarkGray

$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$projectRoot = Split-Path -Parent $scriptPath

Write-Section "🎯 Quick Start Overview"

Write-Host "    This script will:" -ForegroundColor White
Write-Host "    ✅ Check and install dependencies" -ForegroundColor Green
Write-Host "    ✅ Start Docker Desktop if needed" -ForegroundColor Green
Write-Host "    ✅ Configure environment variables" -ForegroundColor Green
Write-Host "    ✅ Start PostgreSQL and Redis containers" -ForegroundColor Green
Write-Host "    ✅ Apply database migrations" -ForegroundColor Green
Write-Host "    ✅ Verify all services are running" -ForegroundColor Green
Write-Host "    ✅ Launch the API" -ForegroundColor Green
Write-Host ""

if (-not $Force) {
    $continue = Read-Host "    Ready to start? (Y/n)"
    if ($continue -eq 'n' -or $continue -eq 'N') {
        Write-Host "    👋 Setup cancelled. Run .\scripts\dev-menu.ps1 for more options." -ForegroundColor Gray
        exit 0
    }
}

Write-Section "🔍 Step 1: Dependency Check"

# Check .NET SDK
Write-Step "Checking .NET SDK..." "INFO"
try {
    $dotnetVersion = dotnet --version
    Write-Step ".NET SDK found: $dotnetVersion" "SUCCESS"
} catch {
    Write-Step ".NET SDK not found" "ERROR"
    Write-Host "    📥 Please install .NET 8 SDK from: https://dotnet.microsoft.com/download/dotnet/8.0" -ForegroundColor Yellow
    exit 1
}

# Check Docker
if (-not $SkipDocker) {
    Write-Step "Checking Docker..." "INFO"

    # Test if Docker is working by running a simple command
    try {
        $dockerTest = docker ps 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Step "Docker is working correctly" "SUCCESS"
        } else {
            # Docker command failed, check why
            if ($dockerTest -like "*docker daemon*" -or $dockerTest -like "*Cannot connect*" -or $dockerTest -like "*dockerDesktopLinuxEngine*") {
                Write-Step "Docker is installed but not running" "WARNING"
                Write-Host "    🐳 Attempting to start Docker Desktop..." -ForegroundColor Cyan

                # Try to start Docker Desktop using Windows start command
                try {
                    Start-Process -FilePath "cmd.exe" -ArgumentList "/c", "start", "Docker Desktop" -WindowStyle Hidden
                    Write-Step "Docker Desktop startup initiated" "INFO"

                    # Wait for Docker to be ready
                    Write-Step "Waiting for Docker to start (up to 2 minutes)..." "WAIT"
                    $timeout = 120
                    $elapsed = 0

                    while ($elapsed -lt $timeout) {
                        Start-Sleep -Seconds 5
                        $elapsed += 5

                        try {
                            docker ps | Out-Null 2>&1
                            if ($LASTEXITCODE -eq 0) {
                                Write-Step "Docker is now ready!" "SUCCESS"
                                break
                            }
                        } catch {
                            # Still waiting
                        }

                        Write-Host "`r    ⏳ Waiting for Docker... ${elapsed}s elapsed" -NoNewline -ForegroundColor Magenta
                    }

                    Write-Host "" # New line

                    if ($elapsed -ge $timeout) {
                        Write-Step "Docker failed to start within timeout" "ERROR"
                        Write-Host "    🔧 Please start Docker Desktop manually:" -ForegroundColor Yellow
                        Write-Host "    1. Press Windows key and type 'Docker Desktop'" -ForegroundColor Gray
                        Write-Host "    2. Click Docker Desktop to start it" -ForegroundColor Gray
                        Write-Host "    3. Wait for Docker icon in system tray" -ForegroundColor Gray
                        Write-Host "    4. Run this script again" -ForegroundColor Gray
                        exit 1
                    }
                } catch {
                    Write-Step "Failed to start Docker Desktop automatically" "ERROR"
                    Write-Host "    🔧 Please start Docker Desktop manually:" -ForegroundColor Yellow
                    Write-Host "    1. Press Windows key and type 'Docker Desktop'" -ForegroundColor Gray
                    Write-Host "    2. Click Docker Desktop to start it" -ForegroundColor Gray
                    Write-Host "    3. Wait for Docker icon in system tray" -ForegroundColor Gray
                    Write-Host "    4. Run this script again" -ForegroundColor Gray
                    exit 1
                }
            } else {
                Write-Step "Docker is not installed" "ERROR"
                Write-Host "    📥 Please install Docker Desktop from: https://www.docker.com/products/docker-desktop" -ForegroundColor Yellow
                exit 1
            }
        }
    } catch {
        Write-Step "Docker command failed" "ERROR"
        Write-Host "    📥 Please install Docker Desktop from: https://www.docker.com/products/docker-desktop" -ForegroundColor Yellow
        exit 1
    }
} else {
    Write-Step "Skipping Docker check (--SkipDocker specified)" "WARNING"
}

Write-Section "⚙️ Step 2: Configuration Setup"

if (-not $SkipConfig) {
    $envFile = Join-Path $projectRoot ".env"
    if (-not (Test-Path $envFile)) {
        Write-Step "No configuration found, running quick setup..." "INFO"
        & "$scriptPath\config-manager.ps1" -Local
        if ($LASTEXITCODE -ne 0) {
            Write-Step "Configuration setup failed" "ERROR"
            exit 1
        }
    } else {
        Write-Step "Configuration file found" "SUCCESS"
    }
} else {
    Write-Step "Skipping configuration setup (--SkipConfig specified)" "WARNING"
}

Write-Section "🚀 Step 3: Development Environment"

Write-Step "Starting development environment..." "INFO"
& "$scriptPath\start-dev-services.ps1"
if ($LASTEXITCODE -ne 0) {
    Write-Step "Failed to start development environment" "ERROR"
    Write-Host ""
    Write-Host "    🔧 Troubleshooting:" -ForegroundColor Yellow
    Write-Host "    ├─ Check if Docker Desktop is running" -ForegroundColor Gray
    Write-Host "    ├─ Run: .\scripts\start-docker.ps1 -Wait" -ForegroundColor Gray
    Write-Host "    ├─ Run: .\scripts\dev-menu.ps1 for interactive setup" -ForegroundColor Gray
    Write-Host "    └─ Check logs above for specific errors" -ForegroundColor Gray
    exit 1
}

Write-Section "🧪 Step 4: Service Verification"

Write-Step "Verifying services are ready..." "INFO"

# Test PostgreSQL
try {
    docker exec notify-postgres psql -U postgres -c "SELECT 1;" | Out-Null
    Write-Step "PostgreSQL: Ready" "SUCCESS"
} catch {
    Write-Step "PostgreSQL: Not responding" "ERROR"
}

# Test Redis
try {
    $redisPing = docker exec notify-redis redis-cli ping 2>$null
    if ($redisPing -eq "PONG") {
        Write-Step "Redis: Ready" "SUCCESS"
    } else {
        Write-Step "Redis: Not responding" "ERROR"
    }
} catch {
    Write-Step "Redis: Not responding" "ERROR"
}

Write-Section "🏃‍♂️ Step 5: Launch API"

Write-Step "Starting the Notify Service API..." "INFO"
Write-Host ""
Write-Host "    🎉 Environment setup complete!" -ForegroundColor Green
Write-Host ""
Write-Host "    📊 Services Status:" -ForegroundColor White
Write-Host "    ├─ 🗄️  PostgreSQL: localhost:5432" -ForegroundColor Gray
Write-Host "    ├─ 🔴 Redis: localhost:6379" -ForegroundColor Gray
Write-Host "    └─ 🚀 API: Starting..." -ForegroundColor Gray
Write-Host ""
Write-Host "    🌐 Once the API starts, you can access:" -ForegroundColor White
Write-Host "    ├─ API Documentation: https://localhost:5001/scalar" -ForegroundColor Cyan
Write-Host "    ├─ Health Check: https://localhost:5001/health" -ForegroundColor Cyan
Write-Host "    └─ OpenAPI Spec: https://localhost:5001/openapi/v1.json" -ForegroundColor Cyan
Write-Host ""
Write-Host "    🛑 To stop services later:" -ForegroundColor White
Write-Host "    └─ .\scripts\stop-dev-services.ps1" -ForegroundColor Red
Write-Host ""
Write-Host "═══════════════════════════════════════════════════════════════════════════════" -ForegroundColor DarkGray
Write-Host ""
Write-Host "🚀 Starting API... (Press Ctrl+C to stop)" -ForegroundColor Green
Write-Host ""

# Change to project root and start the API
Set-Location $projectRoot
try {
    dotnet run --project Presentations\WebApi
} catch {
    Write-Host ""
    Write-Step "API startup interrupted" "WARNING"
    Write-Host ""
    Write-Host "    🔄 To restart the API later:" -ForegroundColor White
    Write-Host "    └─ dotnet run --project Presentations\WebApi" -ForegroundColor Cyan
    Write-Host ""
}
