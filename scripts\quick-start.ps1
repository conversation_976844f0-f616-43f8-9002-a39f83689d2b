# ═══════════════════════════════════════════════════════════════════════════════
# ⚡ Notify Service API - Quick Start Script
# ═══════════════════════════════════════════════════════════════════════════════
# One-command setup for the entire development environment

param(
    [switch]$SkipDocker,
    [switch]$SkipConfig,
    [switch]$Force
)

# Colors and functions
function Write-Step {
    param([string]$Message, [string]$Status = "INFO")
    $timestamp = Get-Date -Format "HH:mm:ss"
    switch ($Status) {
        "SUCCESS" { Write-Host "[$timestamp] ✅ $Message" -ForegroundColor Green }
        "ERROR"   { Write-Host "[$timestamp] ❌ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "[$timestamp] ⚠️  $Message" -ForegroundColor Yellow }
        "INFO"    { Write-Host "[$timestamp] 🔄 $Message" -ForegroundColor Cyan }
        "WAIT"    { Write-Host "[$timestamp] ⏳ $Message" -ForegroundColor Magenta }
    }
}

function Write-Section {
    param([string]$Title)
    Write-Host ""
    Write-Host "┌─────────────────────────────────────────────────────────────────────────────┐" -ForegroundColor DarkGray
    Write-Host "│ $($Title.PadRight(75)) │" -ForegroundColor White
    Write-Host "└─────────────────────────────────────────────────────────────────────────────┘" -ForegroundColor DarkGray
}

function Show-DockerManualInstructions {
    Write-Host ""
    Write-Host "    🐳 Please start Docker Desktop manually:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "    📋 Step-by-step instructions:" -ForegroundColor White
    Write-Host "    1. Press Windows key (⊞)" -ForegroundColor Gray
    Write-Host "    2. Type 'Docker Desktop'" -ForegroundColor Gray
    Write-Host "    3. Click on 'Docker Desktop' when it appears" -ForegroundColor Gray
    Write-Host "    4. Wait for Docker Desktop to start (1-2 minutes)" -ForegroundColor Gray
    Write-Host "    5. Look for Docker whale icon in system tray" -ForegroundColor Gray
    Write-Host "    6. When ready, run this script again" -ForegroundColor Gray
    Write-Host ""
    Write-Host "    💡 Alternative methods:" -ForegroundColor White
    Write-Host "    • Double-click Docker Desktop icon on desktop" -ForegroundColor Gray
    Write-Host "    • Right-click Docker icon in system tray → 'Start'" -ForegroundColor Gray
    Write-Host ""
    Write-Host "    📥 If Docker Desktop is not installed:" -ForegroundColor White
    Write-Host "    └─ Download from: https://www.docker.com/products/docker-desktop" -ForegroundColor Cyan
    Write-Host ""
}

# Header
Clear-Host
Write-Host ""
Write-Host "    ⚡⚡⚡ NOTIFY SERVICE API - QUICK START ⚡⚡⚡" -ForegroundColor Yellow
Write-Host ""
Write-Host "    🚀 Complete Development Environment Setup" -ForegroundColor Cyan
Write-Host "    🎯 From Zero to Running API in Minutes" -ForegroundColor Gray
Write-Host ""
Write-Host "═══════════════════════════════════════════════════════════════════════════════" -ForegroundColor DarkGray

$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$projectRoot = Split-Path -Parent $scriptPath

Write-Section "🎯 Quick Start Overview"

Write-Host "    This script will:" -ForegroundColor White
Write-Host "    ✅ Check and install dependencies" -ForegroundColor Green
Write-Host "    ✅ Start Docker Desktop if needed" -ForegroundColor Green
Write-Host "    ✅ Configure environment variables" -ForegroundColor Green
Write-Host "    ✅ Start PostgreSQL and Redis containers" -ForegroundColor Green
Write-Host "    ✅ Apply database migrations" -ForegroundColor Green
Write-Host "    ✅ Verify all services are running" -ForegroundColor Green
Write-Host "    ✅ Launch the API" -ForegroundColor Green
Write-Host ""

if (-not $Force) {
    $continue = Read-Host "    Ready to start? (Y/n)"
    if ($continue -eq 'n' -or $continue -eq 'N') {
        Write-Host "    👋 Setup cancelled. Run .\scripts\dev-menu.ps1 for more options." -ForegroundColor Gray
        exit 0
    }
}

Write-Section "🔍 Step 1: Dependency Check"

# Check .NET SDK
Write-Step "Checking .NET SDK..." "INFO"
try {
    $dotnetVersion = dotnet --version
    Write-Step ".NET SDK found: $dotnetVersion" "SUCCESS"
} catch {
    Write-Step ".NET SDK not found" "ERROR"
    Write-Host "    📥 Please install .NET 8 SDK from: https://dotnet.microsoft.com/download/dotnet/8.0" -ForegroundColor Yellow
    exit 1
}

# Check Docker
if (-not $SkipDocker) {
    Write-Step "Checking Docker..." "INFO"
    try {
        docker --version | Out-Null
        Write-Step "Docker CLI found" "SUCCESS"
        
        # Check if Docker daemon is running
        try {
            docker info | Out-Null 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Step "Docker daemon is running" "SUCCESS"
            } else {
                throw "Docker daemon not running"
            }
        } catch {
            Write-Step "Docker daemon is not running" "WARNING"
            Write-Host "    🐳 Starting Docker Desktop..." -ForegroundColor Cyan

            # Try the simple Docker helper first
            $dockerHelperPath = Join-Path $scriptPath "start-docker-simple.ps1"
            if (Test-Path $dockerHelperPath) {
                & $dockerHelperPath
                if ($LASTEXITCODE -ne 0) {
                    Write-Step "Automatic Docker startup failed" "ERROR"
                    Show-DockerManualInstructions
                    exit 1
                }
            } else {
                Write-Step "Docker helper script not found" "ERROR"
                Show-DockerManualInstructions
                exit 1
            }
        }
    } catch {
        Write-Step "Docker not found" "ERROR"
        Write-Host "    📥 Please install Docker Desktop from: https://www.docker.com/products/docker-desktop" -ForegroundColor Yellow
        exit 1
    }
} else {
    Write-Step "Skipping Docker check (--SkipDocker specified)" "WARNING"
}

Write-Section "⚙️ Step 2: Configuration Setup"

if (-not $SkipConfig) {
    $envFile = Join-Path $projectRoot ".env"
    if (-not (Test-Path $envFile)) {
        Write-Step "No configuration found, running quick setup..." "INFO"
        & "$scriptPath\config-manager.ps1" -Local
        if ($LASTEXITCODE -ne 0) {
            Write-Step "Configuration setup failed" "ERROR"
            exit 1
        }
    } else {
        Write-Step "Configuration file found" "SUCCESS"
    }
} else {
    Write-Step "Skipping configuration setup (--SkipConfig specified)" "WARNING"
}

Write-Section "🚀 Step 3: Development Environment"

Write-Step "Starting development environment..." "INFO"
& "$scriptPath\start-dev-services.ps1"
if ($LASTEXITCODE -ne 0) {
    Write-Step "Failed to start development environment" "ERROR"
    Write-Host ""
    Write-Host "    🔧 Troubleshooting:" -ForegroundColor Yellow
    Write-Host "    ├─ Check if Docker Desktop is running" -ForegroundColor Gray
    Write-Host "    ├─ Run: .\scripts\start-docker.ps1 -Wait" -ForegroundColor Gray
    Write-Host "    ├─ Run: .\scripts\dev-menu.ps1 for interactive setup" -ForegroundColor Gray
    Write-Host "    └─ Check logs above for specific errors" -ForegroundColor Gray
    exit 1
}

Write-Section "🧪 Step 4: Service Verification"

Write-Step "Verifying services are ready..." "INFO"

# Test PostgreSQL
try {
    docker exec notify-postgres psql -U postgres -c "SELECT 1;" | Out-Null
    Write-Step "PostgreSQL: Ready" "SUCCESS"
} catch {
    Write-Step "PostgreSQL: Not responding" "ERROR"
}

# Test Redis
try {
    $redisPing = docker exec notify-redis redis-cli ping 2>$null
    if ($redisPing -eq "PONG") {
        Write-Step "Redis: Ready" "SUCCESS"
    } else {
        Write-Step "Redis: Not responding" "ERROR"
    }
} catch {
    Write-Step "Redis: Not responding" "ERROR"
}

Write-Section "🏃‍♂️ Step 5: Launch API"

Write-Step "Starting the Notify Service API..." "INFO"
Write-Host ""
Write-Host "    🎉 Environment setup complete!" -ForegroundColor Green
Write-Host ""
Write-Host "    📊 Services Status:" -ForegroundColor White
Write-Host "    ├─ 🗄️  PostgreSQL: localhost:5432" -ForegroundColor Gray
Write-Host "    ├─ 🔴 Redis: localhost:6379" -ForegroundColor Gray
Write-Host "    └─ 🚀 API: Starting..." -ForegroundColor Gray
Write-Host ""
Write-Host "    🌐 Once the API starts, you can access:" -ForegroundColor White
Write-Host "    ├─ API Documentation: https://localhost:5001/scalar" -ForegroundColor Cyan
Write-Host "    ├─ Health Check: https://localhost:5001/health" -ForegroundColor Cyan
Write-Host "    └─ OpenAPI Spec: https://localhost:5001/openapi/v1.json" -ForegroundColor Cyan
Write-Host ""
Write-Host "    🛑 To stop services later:" -ForegroundColor White
Write-Host "    └─ .\scripts\stop-dev-services.ps1" -ForegroundColor Red
Write-Host ""
Write-Host "═══════════════════════════════════════════════════════════════════════════════" -ForegroundColor DarkGray
Write-Host ""
Write-Host "🚀 Starting API... (Press Ctrl+C to stop)" -ForegroundColor Green
Write-Host ""

# Change to project root and start the API
Set-Location $projectRoot
try {
    dotnet run --project Presentations\WebApi
} catch {
    Write-Host ""
    Write-Step "API startup interrupted" "WARNING"
    Write-Host ""
    Write-Host "    🔄 To restart the API later:" -ForegroundColor White
    Write-Host "    └─ dotnet run --project Presentations\WebApi" -ForegroundColor Cyan
    Write-Host ""
}
