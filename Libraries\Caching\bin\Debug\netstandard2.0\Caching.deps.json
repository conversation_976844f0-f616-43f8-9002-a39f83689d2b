{"runtimeTarget": {"name": ".NETStandard,Version=v2.0/", "signature": ""}, "compilationOptions": {}, "targets": {".NETStandard,Version=v2.0": {}, ".NETStandard,Version=v2.0/": {"Caching/1.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Models": "1.0.0", "Newtonsoft.Json": "13.0.3", "RedLock.net": "2.2.0", "StackExchange.Redis": "2.1.58"}, "runtime": {"Caching.dll": {}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.0.0.17205"}}}, "Microsoft.Extensions.Logging/2.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.0.0", "Microsoft.Extensions.Logging.Abstractions": "2.0.0", "Microsoft.Extensions.Options": "2.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "2.0.0.17205"}}}, "Microsoft.Extensions.Logging.Abstractions/2.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.0.0.17205"}}}, "Microsoft.Extensions.Options/2.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "2.0.0.17205"}}}, "Microsoft.Extensions.Primitives/8.0.0": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "Pipelines.Sockets.Unofficial/2.1.16": {"dependencies": {"System.Buffers": "4.5.1", "System.IO.Pipelines": "4.7.1", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.0/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.16.58714"}}}, "RedLock.net/2.2.0": {"dependencies": {"Microsoft.Extensions.Logging": "2.0.0", "Microsoft.Extensions.Logging.Abstractions": "2.0.0", "StackExchange.Redis": "2.1.58"}, "runtime": {"lib/netstandard2.0/RedLockNet.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/netstandard2.0/RedLockNet.SERedis.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "StackExchange.Redis/2.1.58": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "Pipelines.Sockets.Unofficial": "2.1.16", "System.Diagnostics.PerformanceCounter": "4.7.0", "System.Threading.Channels": "4.7.1"}, "runtime": {"lib/netstandard2.0/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.58.34321"}}}, "System.Buffers/4.5.1": {"runtime": {"lib/netstandard2.0/System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.28619.1"}}}, "System.ComponentModel.Annotations/5.0.0": {"runtime": {"lib/netstandard2.0/System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Diagnostics.PerformanceCounter/4.7.0": {"runtime": {"lib/netstandard2.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.IO.Pipelines/4.7.1": {"dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.12001"}}}, "System.Memory/4.5.5": {"dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.4.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.31308.1"}}}, "System.Numerics.Vectors/4.4.0": {"runtime": {"lib/netstandard2.0/System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Threading.Channels/4.7.1": {"dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "4.2.0.1", "fileVersion": "4.6.28619.1"}}}, "Models/1.0.0": {"dependencies": {"System.ComponentModel.Annotations": "5.0.0"}, "runtime": {"Models.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Caching/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "path": "microsoft.extensions.configuration.binder/8.0.0", "hashPath": "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eUdJ0Q/GfVyUJc0Jal5L1QZLceL78pvEM9wEKcHeI24KorqMDoVX+gWsMGLulQMfOwsUaPtkpQM2pFERTzSfSg==", "path": "microsoft.extensions.dependencyinjection.abstractions/2.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VP10syWV/vxYYMKgZ2eDESmUsz3gPxvBn5J6tkVN8lI4M+dF43RN8fWsEPbcAneDmZrHl3Pv23z05nmyGkJlpg==", "path": "microsoft.extensions.logging/2.0.0", "hashPath": "microsoft.extensions.logging.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6ZCllUYGFukkymSTx3Yr0G/ajRxoNJp7/FqSxSB4fGISST54ifBhgu4Nc0ItGi3i6DqwuNd8SUyObmiC++AO2Q==", "path": "microsoft.extensions.logging.abstractions/2.0.0", "hashPath": "microsoft.extensions.logging.abstractions.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-sAKBgjl2gWsECBLLR9K54T7/uZaP2n9GhMYHay/oOLfvpvX0+iNAlQ2NJgVE352C9Fs5CDV3VbNTK8T2aNKQFA==", "path": "microsoft.extensions.options/2.0.0", "hashPath": "microsoft.extensions.options.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.1.16": {"type": "package", "serviceable": true, "sha512": "sha512-kIMCOfHBr+BRrUdz0zSGAGKC4lVO4pT4Py6XHVaGCV3mppc1BJWcbxYDEVLBFIBu6DMGq2rVRKcl6odx8lMGwg==", "path": "pipelines.sockets.unofficial/2.1.16", "hashPath": "pipelines.sockets.unofficial.2.1.16.nupkg.sha512"}, "RedLock.net/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-lud+oD8W2VKen5BtiPLahvVK1/Tmgla9KhWYbqy+m51Hg8ip4ymi8rrmFdSzVEZ+RfAhdKg37xNmh3LqBr2dKg==", "path": "redlock.net/2.2.0", "hashPath": "redlock.net.2.2.0.nupkg.sha512"}, "StackExchange.Redis/2.1.58": {"type": "package", "serviceable": true, "sha512": "sha512-4DhbxL0SE/jNdX8TmqHHmu/wv4alOHsWJiXUTZAhqelnSkczikdKoWhPqUUCzfAp6tX5AKUKxEPE0dHVXOyMzA==", "path": "stackexchange.redis/2.1.58", "hashPath": "stackexchange.redis.2.1.58.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-kE9szT4i3TYT9bDE/BPfzg9/BL6enMiZlcUmnUEBrhRtxWvurKoa8qhXkLTRhrxMzBqaDleWlRfIPE02tulU+w==", "path": "system.diagnostics.performancecounter/4.7.0", "hashPath": "system.diagnostics.performancecounter.4.7.0.nupkg.sha512"}, "System.IO.Pipelines/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-TyMTasnXQt7U4k13RQ7tA3CrfWEkvWjR635SFfnKVwwMOgClLE5mdHSkG2D13BLIdNwbPTGVQnhQB8Rk7f53Rg==", "path": "system.io.pipelines/4.7.1", "hashPath": "system.io.pipelines.4.7.1.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Numerics.Vectors/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-UiLzLW+Lw6HLed1Hcg+8jSRttrbuXv7DANVj0DkL9g6EnnzbL75EB7EWsw5uRbhxd/4YdG8li5XizGWepmG3PQ==", "path": "system.numerics.vectors/4.4.0", "hashPath": "system.numerics.vectors.4.4.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Threading.Channels/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-6akRtHK/wab3246t4p5v3HQrtQk8LboOt5T4dtpNgsp3zvDeM4/Gx8V12t0h+c/W9/enUrilk8n6EQqdQorZAA==", "path": "system.threading.channels/4.7.1", "hashPath": "system.threading.channels.4.7.1.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "Models/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}