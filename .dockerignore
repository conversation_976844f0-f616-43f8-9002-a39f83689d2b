# =========================
# .dockerignore for Notify Service API
# Optimized for production builds
# =========================

# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
LICENSE
CHANGELOG.md
docs/
*.md

# IDE and Editor files
.vs/
.vscode/
.idea/
*.swp
*.swo
*~

# Build outputs
**/bin/
**/obj/
**/out/
**/publish/

# NuGet
**/packages/
*.nupkg

# Test results
TestResults/
**/*.trx
**/*.coverage
**/*.coveragexml

# User-specific files
*.suo
*.user
*.userosscache
*.sln.docstates

# Environment files
.env
.env.*
!.env.template

# Docker files (avoid recursion)
**/Dockerfile*
**/docker-compose*
.dockerignore

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# Backup files
*.bak
*.backup
*.tmp

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Azure
.azure/

# Scripts (deployment scripts shouldn't be in container)
scripts/

# Configuration files that shouldn't be in container
config/ssl/
config/secrets/

# Database files
*.db
*.sqlite
*.sqlite3

# Certificate files
*.pfx
*.p12
*.key
*.crt
*.pem

# Monitoring and profiling
*.psess
*.vsp
*.vspx

# Visual Studio cache files
*.aps
*.ncb
*.opendb
*.opensdf
*.sdf
*.cachefile
*.VC.db
*.VC.VC.opendb

# Visual Studio profiler
*.psess
*.vsp
*.vspx
*.sap

# TFS 2012 Local Workspace
$tf/

# Guidance Automation Toolkit
*.gpState

# ReSharper
_ReSharper*/
*.[Rr]e[Ss]harper
*.DotSettings.user

# JustCode
.JustCode

# TeamCity
_TeamCity*

# DotCover
*.dotCover

# NCrunch
_NCrunch_*
.*crunch*.local.xml
nCrunchTemp_*

# MightyMoose
*.mm.*
AutoTest.Net/

# Web workbench (sass)
.sass-cache/

# Installshield output folder
[Ee]xpress/

# DocProject
DocProject/buildhelp/
DocProject/Help/*.HxT
DocProject/Help/*.HxC
DocProject/Help/Html2
DocProject/Help/html

# Click-Once directory
publish/

# Publish Web Output
*.[Pp]ublish.xml
*.azurePubxml
*.pubxml
*.publishproj

# Microsoft Azure Web App publish settings
PublishScripts/

# NuGet Packages
*.nupkg
**/packages/*
!**/packages/build/

# Windows Azure Build Output
csx/
*.build.csdef

# Windows Store app package directory
AppPackages/

# Visual Studio cache files
*.[Cc]ache
!*.[Cc]ache/

# Others
ClientBin/
~$*
*~
*.dbmdl
*.dbproj.schemaview
*.pfx
*.publishsettings
orleans.codegen.cs

# RIA/Silverlight projects
Generated_Code/

# Backup & report files
_UpgradeReport_Files/
Backup*/
UpgradeLog*.XML
UpgradeLog*.htm

# SQL Server files
*.mdf
*.ldf
*.ndf

# Business Intelligence projects
*.rdl.data
*.bim.layout
*.bim_*.settings

# Microsoft Fakes
FakesAssemblies/

# GhostDoc plugin setting file
*.GhostDoc.xml

# Node.js Tools for Visual Studio
.ntvs_analysis.dat

# Visual Studio 6 build log
*.plg

# Visual Studio 6 workspace options file
*.opt

# Visual Studio LightSwitch build output
**/*.HTMLClient/GeneratedArtifacts
**/*.DesktopClient/GeneratedArtifacts
**/*.DesktopClient/ModelManifest.xml
**/*.Server/GeneratedArtifacts
**/*.Server/ModelManifest.xml
_Pvt_Extensions

# Paket dependency manager
.paket/paket.exe
paket-files/

# FAKE - F# Make
.fake/

# JetBrains Rider
.idea/
*.sln.iml

# CodeRush
.cr/

# Python Tools for Visual Studio (PTVS)
__pycache__/
*.pyc

# Cake
tools/

# Telerik's JustMock configuration file
*.jmconfig

# BizTalk build output
*.btp.cs
*.btm.cs
*.odx.cs
*.xsd.cs
!**/.gitignore
!.git/HEAD
!.git/config
!.git/packed-refs
!.git/refs/heads/**