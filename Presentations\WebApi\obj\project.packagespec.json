﻿"restore":{"projectUniqueName":"C:\\Users\\<USER>\\OneDrive\\Documentos\\New folder\\Presentations\\WebApi\\WebApi.csproj","projectName":"WebApi","projectPath":"C:\\Users\\<USER>\\OneDrive\\Documentos\\New folder\\Presentations\\WebApi\\WebApi.csproj","outputPath":"C:\\Users\\<USER>\\OneDrive\\Documentos\\New folder\\Presentations\\WebApi\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["net9.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"C:\\Users\\<USER>\\OneDrive\\Documentos\\New folder\\Libraries\\Caching\\Caching.csproj":{"projectPath":"C:\\Users\\<USER>\\OneDrive\\Documentos\\New folder\\Libraries\\Caching\\Caching.csproj"},"C:\\Users\\<USER>\\OneDrive\\Documentos\\New folder\\Libraries\\Core\\Core.csproj":{"projectPath":"C:\\Users\\<USER>\\OneDrive\\Documentos\\New folder\\Libraries\\Core\\Core.csproj"},"C:\\Users\\<USER>\\OneDrive\\Documentos\\New folder\\Libraries\\Identity\\Identity.csproj":{"projectPath":"C:\\Users\\<USER>\\OneDrive\\Documentos\\New folder\\Libraries\\Identity\\Identity.csproj"},"C:\\Users\\<USER>\\OneDrive\\Documentos\\New folder\\Libraries\\Models\\Models.csproj":{"projectPath":"C:\\Users\\<USER>\\OneDrive\\Documentos\\New folder\\Libraries\\Models\\Models.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"10.0.100"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"AspNetCore.HealthChecks.Redis":{"target":"Package","version":"[9.0.0, )"},"AspNetCore.HealthChecks.SqlServer":{"target":"Package","version":"[9.0.0, )"},"AspNetCore.HealthChecks.UI.Client":{"target":"Package","version":"[9.0.0, )"},"AutoMapper.Extensions.Microsoft.DependencyInjection":{"target":"Package","version":"[12.0.1, )"},"GraphQL":{"target":"Package","version":"[8.5.0, )"},"Microsoft.AspNetCore.Mvc.NewtonsoftJson":{"target":"Package","version":"[8.0.0, )"},"Microsoft.EntityFrameworkCore.Design":{"include":"Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive","suppressParent":"All","target":"Package","version":"[9.0.6, )"},"Microsoft.Extensions.Logging":{"target":"Package","version":"[9.0.6, )"},"Microsoft.VisualStudio.Azure.Containers.Tools.Targets":{"target":"Package","version":"[1.21.2, )"},"Newtonsoft.Json":{"target":"Package","version":"[13.0.3, )"},"Serilog.AspNetCore":{"target":"Package","version":"[9.0.0, )"},"Serilog.Sinks.File":{"target":"Package","version":"[7.0.0, )"},"Serilog.Sinks.Seq":{"target":"Package","version":"[9.0.0, )"},"graphiql":{"target":"Package","version":"[2.0.0, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"downloadDependencies":[{"name":"Microsoft.AspNetCore.App.Ref","version":"[9.0.5, 9.0.5]"},{"name":"Microsoft.NETCore.App.Host.win-x64","version":"[9.0.5, 9.0.5]"},{"name":"Microsoft.NETCore.App.Ref","version":"[9.0.5, 9.0.5]"},{"name":"Microsoft.WindowsDesktop.App.Ref","version":"[9.0.5, 9.0.5]"}],"frameworkReferences":{"Microsoft.AspNetCore.App":{"privateAssets":"none"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.5.25277.114/PortableRuntimeIdentifierGraph.json"}}