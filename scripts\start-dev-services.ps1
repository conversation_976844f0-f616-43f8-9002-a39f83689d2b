# ═══════════════════════════════════════════════════════════════════════════════
# 🚀 Notify Service API - Development Environment Setup
# ═══════════════════════════════════════════════════════════════════════════════
# This script starts PostgreSQL and Redis using Docker and applies migrations

# ASCII Art Header
Write-Host ""
Write-Host "    ███╗   ██╗ ██████╗ ████████╗██╗███████╗██╗   ██╗" -ForegroundColor Cyan
Write-Host "    ████╗  ██║██╔═══██╗╚══██╔══╝██║██╔════╝╚██╗ ██╔╝" -ForegroundColor Cyan
Write-Host "    ██╔██╗ ██║██║   ██║   ██║   ██║█████╗   ╚████╔╝ " -ForegroundColor Cyan
Write-Host "    ██║╚██╗██║██║   ██║   ██║   ██║██╔══╝    ╚██╔╝  " -ForegroundColor Cyan
Write-Host "    ██║ ╚████║╚██████╔╝   ██║   ██║██║        ██║   " -ForegroundColor Cyan
Write-Host "    ╚═╝  ╚═══╝ ╚═════╝    ╚═╝   ╚═╝╚═╝        ╚═╝   " -ForegroundColor Cyan
Write-Host ""
Write-Host "    🔧 Development Environment Setup" -ForegroundColor Yellow
Write-Host "    📦 Docker • PostgreSQL • Redis • Migrations" -ForegroundColor Gray
Write-Host ""
Write-Host "═══════════════════════════════════════════════════════════════════════════════" -ForegroundColor DarkGray

# Configuration
$ProjectRoot = Split-Path -Parent (Split-Path -Parent $MyInvocation.MyCommand.Path)
$PostgresContainer = "notify-postgres"
$RedisContainer = "notify-redis"
$PostgresPassword = "postgres"
$DatabaseNames = @("NotifyDb", "NotifyIdentityDb")

function Write-Step {
    param([string]$Message, [string]$Status = "INFO")
    $timestamp = Get-Date -Format "HH:mm:ss"
    switch ($Status) {
        "SUCCESS" { Write-Host "[$timestamp] ✅ $Message" -ForegroundColor Green }
        "ERROR"   { Write-Host "[$timestamp] ❌ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "[$timestamp] ⚠️  $Message" -ForegroundColor Yellow }
        "INFO"    { Write-Host "[$timestamp] 🔄 $Message" -ForegroundColor Cyan }
        "WAIT"    { Write-Host "[$timestamp] ⏳ $Message" -ForegroundColor Magenta }
    }
}

function Write-Section {
    param([string]$Title)
    Write-Host ""
    Write-Host "┌─────────────────────────────────────────────────────────────────────────────┐" -ForegroundColor DarkGray
    Write-Host "│ $($Title.PadRight(75)) │" -ForegroundColor White
    Write-Host "└─────────────────────────────────────────────────────────────────────────────┘" -ForegroundColor DarkGray
}

Write-Step "Initializing development environment setup..." "INFO"

Write-Section "🐳 Docker Environment Check"

try {
    Write-Step "Checking Docker installation..." "INFO"
    docker version | Out-Null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker is not running"
    }
    Write-Step "Docker is running and accessible" "SUCCESS"
} catch {
    Write-Step "Docker is not running or not installed" "ERROR"
    Write-Host "    📥 Please install Docker Desktop and start it" -ForegroundColor Yellow
    Write-Host "    🔗 Download from: https://www.docker.com/products/docker-desktop" -ForegroundColor Cyan
    Write-Host ""
    exit 1
}

Write-Section "🗄️ PostgreSQL Database Setup"

Write-Step "Starting PostgreSQL container..." "INFO"
docker run -d `
    --name $PostgresContainer `
    -e POSTGRES_PASSWORD=$PostgresPassword `
    -e POSTGRES_DB=postgres `
    -p 5432:5432 `
    postgres:15-alpine 2>$null | Out-Null

if ($LASTEXITCODE -eq 0) {
    Write-Step "PostgreSQL container started successfully" "SUCCESS"
} else {
    Write-Step "PostgreSQL container already exists, attempting to start..." "WARNING"
    docker start $PostgresContainer | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Step "Existing PostgreSQL container started" "SUCCESS"
    } else {
        Write-Step "Failed to start PostgreSQL container" "ERROR"
        exit 1
    }
}

Write-Section "🔴 Redis Cache Setup"

Write-Step "Starting Redis container..." "INFO"
docker run -d `
    --name $RedisContainer `
    -p 6379:6379 `
    redis:7-alpine 2>$null

if ($LASTEXITCODE -eq 0) {
    Write-Step "Redis container started successfully" "SUCCESS"
} else {
    Write-Step "Redis container already exists, attempting to start..." "WARNING"
    docker start $RedisContainer | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Step "Existing Redis container started" "SUCCESS"
    } else {
        Write-Step "Failed to start Redis container" "ERROR"
        exit 1
    }
}

Write-Section "⏳ Service Initialization"

Write-Step "Waiting for services to initialize..." "WAIT"
Start-Sleep -Seconds 5

# Wait for PostgreSQL to be ready
$maxAttempts = 30
$attempt = 0
do {
    $attempt++
    Write-Step "Checking PostgreSQL readiness (attempt $attempt/$maxAttempts)..." "INFO"
    docker exec $PostgresContainer pg_isready -U postgres 2>$null | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Step "PostgreSQL is ready!" "SUCCESS"
        break
    }
    Start-Sleep -Seconds 2
} while ($attempt -lt $maxAttempts)

if ($attempt -ge $maxAttempts) {
    Write-Step "PostgreSQL failed to become ready within timeout" "ERROR"
    exit 1
}

Write-Section "🏗️ Database Creation"

foreach ($dbName in $DatabaseNames) {
    Write-Step "Creating database: $dbName..." "INFO"
    docker exec $PostgresContainer psql -U postgres -c "CREATE DATABASE `"$dbName`";" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Step "Database '$dbName' created successfully" "SUCCESS"
    } else {
        Write-Step "Database '$dbName' already exists or creation failed" "WARNING"
    }
}

Write-Section "🔧 Entity Framework Migrations"

# Check if .NET SDK is available
Write-Step "Checking .NET SDK..." "INFO"
try {
    $dotnetVersion = dotnet --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Step ".NET SDK found: $dotnetVersion" "SUCCESS"
    } else {
        Write-Step ".NET SDK not found, skipping migrations" "WARNING"
        $skipMigrations = $true
    }
} catch {
    Write-Step ".NET SDK not available, skipping migrations" "WARNING"
    $skipMigrations = $true
}

if (-not $skipMigrations) {
    # Navigate to project root
    Set-Location $ProjectRoot

    Write-Step "Restoring NuGet packages..." "INFO"
    dotnet restore | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Step "NuGet packages restored successfully" "SUCCESS"
    } else {
        Write-Step "Failed to restore NuGet packages" "ERROR"
    }

    # Check if migrations exist, if not create them
    Write-Step "Checking for existing migrations..." "INFO"

    $dataProjectPath = "Libraries\Data"
    $identityProjectPath = "Libraries\Identity"
    $webApiProjectPath = "Presentations\WebApi"

    # Check Data migrations
    if (-not (Test-Path "$dataProjectPath\Migrations")) {
        Write-Step "Creating initial Data migration..." "INFO"
        dotnet ef migrations add InitialCreate --project $dataProjectPath --startup-project $webApiProjectPath --context ApplicationDbContext
        if ($LASTEXITCODE -eq 0) {
            Write-Step "Data migration created successfully" "SUCCESS"
        } else {
            Write-Step "Failed to create Data migration" "ERROR"
        }
    } else {
        Write-Step "Data migrations already exist" "SUCCESS"
    }

    # Check Identity migrations
    if (-not (Test-Path "$identityProjectPath\Migrations")) {
        Write-Step "Creating initial Identity migration..." "INFO"
        dotnet ef migrations add InitialCreate --project $identityProjectPath --startup-project $webApiProjectPath --context IdentityContext
        if ($LASTEXITCODE -eq 0) {
            Write-Step "Identity migration created successfully" "SUCCESS"
        } else {
            Write-Step "Failed to create Identity migration" "ERROR"
        }
    } else {
        Write-Step "Identity migrations already exist" "SUCCESS"
    }

    # Apply migrations
    Write-Step "Applying Data migrations..." "INFO"
    dotnet ef database update --project $dataProjectPath --startup-project $webApiProjectPath --context ApplicationDbContext
    if ($LASTEXITCODE -eq 0) {
        Write-Step "Data migrations applied successfully" "SUCCESS"
    } else {
        Write-Step "Failed to apply Data migrations" "ERROR"
    }

    Write-Step "Applying Identity migrations..." "INFO"
    dotnet ef database update --project $identityProjectPath --startup-project $webApiProjectPath --context IdentityContext
    if ($LASTEXITCODE -eq 0) {
        Write-Step "Identity migrations applied successfully" "SUCCESS"
    } else {
        Write-Step "Failed to apply Identity migrations" "ERROR"
    }
}

Write-Section "🧪 Connection Testing"

# Test PostgreSQL
Write-Step "Testing PostgreSQL connection..." "INFO"
try {
    docker exec $PostgresContainer psql -U postgres -c "SELECT 1;" | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Step "PostgreSQL connection successful" "SUCCESS"
    } else {
        Write-Step "PostgreSQL connection failed" "ERROR"
    }
} catch {
    Write-Step "PostgreSQL connection test failed" "ERROR"
}

# Test Redis
Write-Step "Testing Redis connection..." "INFO"
try {
    $redisPing = docker exec $RedisContainer redis-cli ping 2>$null
    if ($redisPing -eq "PONG") {
        Write-Step "Redis connection successful" "SUCCESS"
    } else {
        Write-Step "Redis connection failed" "ERROR"
    }
} catch {
    Write-Step "Redis connection test failed" "ERROR"
}

Write-Section "🎉 Setup Complete!"

Write-Host ""
Write-Host "    🚀 Your development environment is ready!" -ForegroundColor Green
Write-Host ""
Write-Host "    📊 Services Status:" -ForegroundColor White
Write-Host "    ├─ 🗄️  PostgreSQL: " -NoNewline -ForegroundColor Gray
Write-Host "localhost:5432" -ForegroundColor Cyan
Write-Host "    │   ├─ Username: " -NoNewline -ForegroundColor Gray
Write-Host "postgres" -ForegroundColor Yellow
Write-Host "    │   ├─ Password: " -NoNewline -ForegroundColor Gray
Write-Host "postgres" -ForegroundColor Yellow
Write-Host "    │   └─ Databases: " -NoNewline -ForegroundColor Gray
Write-Host "NotifyDb, NotifyIdentityDb" -ForegroundColor Yellow
Write-Host "    └─ 🔴 Redis: " -NoNewline -ForegroundColor Gray
Write-Host "localhost:6379" -ForegroundColor Cyan
Write-Host ""
Write-Host "    🏃‍♂️ Next Steps:" -ForegroundColor White
Write-Host "    ├─ Run the API: " -NoNewline -ForegroundColor Gray
Write-Host "dotnet run --project Presentations\WebApi" -ForegroundColor Green
Write-Host "    ├─ API Docs: " -NoNewline -ForegroundColor Gray
Write-Host "https://localhost:5001/scalar" -ForegroundColor Cyan
Write-Host "    └─ Health Check: " -NoNewline -ForegroundColor Gray
Write-Host "https://localhost:5001/health" -ForegroundColor Cyan
Write-Host ""
Write-Host "    🛑 To stop services later:" -ForegroundColor White
Write-Host "    └─ " -NoNewline -ForegroundColor Gray
Write-Host "docker stop $PostgresContainer $RedisContainer" -ForegroundColor Red
Write-Host ""
Write-Host "═══════════════════════════════════════════════════════════════════════════════" -ForegroundColor DarkGray
