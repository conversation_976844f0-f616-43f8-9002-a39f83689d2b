# Quick start script for development services using Docker
# This script starts PostgreSQL and Redis using Docker

Write-Host "Starting development services with Docker..." -ForegroundColor Green

# Check if Dock<PERSON> is running
try {
    docker version | Out-Null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker is not running"
    }
    Write-Host "✓ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker is not running or not installed" -ForegroundColor Red
    Write-Host "Please install Docker Desktop and start it" -ForegroundColor Yellow
    Write-Host "Download from: https://www.docker.com/products/docker-desktop" -ForegroundColor Cyan
    exit 1
}

# Start PostgreSQL
Write-Host "Starting PostgreSQL..." -ForegroundColor Yellow
docker run -d `
    --name notify-postgres `
    -e POSTGRES_PASSWORD=postgres `
    -e POSTGRES_DB=postgres `
    -p 5432:5432 `
    postgres:15-alpine

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ PostgreSQL started successfully" -ForegroundColor Green
} else {
    Write-Host "⚠ PostgreSQL container might already exist" -ForegroundColor Yellow
    # Try to start existing container
    docker start notify-postgres
}

# Start Redis
Write-Host "Starting Redis..." -ForegroundColor Yellow
docker run -d `
    --name notify-redis `
    -p 6379:6379 `
    redis:7-alpine

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Redis started successfully" -ForegroundColor Green
} else {
    Write-Host "⚠ Redis container might already exist" -ForegroundColor Yellow
    # Try to start existing container
    docker start notify-redis
}

# Wait for services to be ready
Write-Host "Waiting for services to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Create databases
Write-Host "Creating databases..." -ForegroundColor Yellow
docker exec notify-postgres psql -U postgres -c 'CREATE DATABASE "NotifyDb";' 2>$null
docker exec notify-postgres psql -U postgres -c 'CREATE DATABASE "NotifyIdentityDb";' 2>$null

Write-Host "✓ Databases created (or already exist)" -ForegroundColor Green

# Test connections
Write-Host "Testing connections..." -ForegroundColor Yellow

# Test PostgreSQL
try {
    docker exec notify-postgres psql -U postgres -c "SELECT 1;" | Out-Null
    Write-Host "✓ PostgreSQL is accessible" -ForegroundColor Green
} catch {
    Write-Host "✗ PostgreSQL connection test failed" -ForegroundColor Red
}

# Test Redis
try {
    docker exec notify-redis redis-cli ping | Out-Null
    Write-Host "✓ Redis is accessible" -ForegroundColor Green
} catch {
    Write-Host "✗ Redis connection test failed" -ForegroundColor Red
}

Write-Host "`n🎉 Development services are ready!" -ForegroundColor Green
Write-Host "PostgreSQL: localhost:5432 (user: postgres, password: postgres)" -ForegroundColor Cyan
Write-Host "Redis: localhost:6379" -ForegroundColor Cyan
Write-Host "`nYou can now run the application with:" -ForegroundColor Green
Write-Host "dotnet run --project Presentations\WebApi" -ForegroundColor Cyan
Write-Host "`nTo stop services later, run:" -ForegroundColor Yellow
Write-Host "docker stop notify-postgres notify-redis" -ForegroundColor Cyan
