﻿<Project Sdk="Microsoft.NET.Sdk">
<PropertyGroup>
  <TargetFramework>net8.0</TargetFramework>
  <Nullable>enable</Nullable>
  <ImplicitUsings>enable</ImplicitUsings>
</PropertyGroup>
<ItemGroup>
  <Compile Remove="Helpers\**" />
  <EmbeddedResource Remove="Helpers\**" />
  <None Remove="Helpers\**" />
</ItemGroup>
<ItemGroup>
  <Compile Remove="Migrations\20201005125852_my_new_migration.cs" />
  <Compile Remove="Migrations\20201005125852_my_new_migration.Designer.cs" />
</ItemGroup>
<ItemGroup>
  <ProjectReference Include="..\Core\Core.csproj" />
  <ProjectReference Include="..\Models\Models.csproj" />
</ItemGroup>
<ItemGroup>
  <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.8" />
  <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.8" />
  <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.8" />
  <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.8" />
  <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.8" />
  <PackageReference Include="Microsoft.Extensions.Identity.Stores" Version="8.0.8" />
  <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.4" />
</ItemGroup>
</Project>
