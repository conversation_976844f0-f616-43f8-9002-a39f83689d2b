# =========================
# Production Dockerfile for Notify Service API
# Optimized for Azure Container Registry and production deployment
# =========================

# Build arguments
ARG BUILD_CONFIGURATION=Release
ARG DOTNET_VERSION=8.0

# =========================
# Build Stage
# =========================
FROM mcr.microsoft.com/dotnet/sdk:${DOTNET_VERSION} AS build
WORKDIR /src

# Install build tools
RUN apt-get update && apt-get install -y \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy solution and project files for better layer caching
COPY Notify.sln ./
COPY Directory.Build.props ./
COPY .editorconfig ./

# Copy all project files
COPY Libraries/Models/Models.csproj Libraries/Models/
COPY Libraries/Data/Data.csproj Libraries/Data/
COPY Libraries/Services/Services.csproj Libraries/Services/
COPY Libraries/Core/Core.csproj Libraries/Core/
COPY Libraries/Identity/Identity.csproj Libraries/Identity/
COPY Libraries/Caching/Caching.csproj Libraries/Caching/
COPY Presentations/WebApi/WebApi.csproj Presentations/WebApi/

# Restore dependencies (cached layer)
RUN dotnet restore --verbosity minimal

# Copy source code
COPY Libraries/ Libraries/
COPY Presentations/ Presentations/

# Build application
RUN dotnet build -c ${BUILD_CONFIGURATION} --no-restore --verbosity minimal

# Publish application
RUN dotnet publish Presentations/WebApi/WebApi.csproj \
    -c ${BUILD_CONFIGURATION} \
    -o /app/publish \
    --no-build \
    --verbosity minimal \
    --self-contained false \
    /p:PublishTrimmed=false

# =========================
# Runtime Stage
# =========================
FROM mcr.microsoft.com/dotnet/aspnet:${DOTNET_VERSION} AS runtime

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Set working directory
WORKDIR /app

# Copy published application
COPY --from=build /app/publish .

# Set ownership to non-root user
RUN chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Configure environment
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:80
ENV DOTNET_RUNNING_IN_CONTAINER=true
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=1

# Expose port
EXPOSE 80

# Add labels for better container management
LABEL maintainer="Notify Service Team"
LABEL version="1.0"
LABEL description="Notify Service API - .NET 8 Web API with PostgreSQL and Redis"
LABEL org.opencontainers.image.source="https://github.com/your-org/notify-service-api"

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:80/health || exit 1

# Start application
ENTRYPOINT ["dotnet", "WebApi.dll"]
