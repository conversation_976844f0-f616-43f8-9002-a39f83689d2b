# ═══════════════════════════════════════════════════════════════════════════════
# 🎛️ Notify Service API - Development Menu
# ═══════════════════════════════════════════════════════════════════════════════
# Interactive menu for development environment management

# ASCII Art Header
function Show-Header {
    Clear-Host
    Write-Host ""
    Write-Host "    ███╗   ██╗ ██████╗ ████████╗██╗███████╗██╗   ██╗" -ForegroundColor Cyan
    Write-Host "    ████╗  ██║██╔═══██╗╚══██╔══╝██║██╔════╝╚██╗ ██╔╝" -ForegroundColor Cyan
    Write-Host "    ██╔██╗ ██║██║   ██║   ██║   ██║█████╗   ╚████╔╝ " -ForegroundColor Cyan
    Write-Host "    ██║╚██╗██║██║   ██║   ██║   ██║██╔══╝    ╚██╔╝  " -ForegroundColor Cyan
    Write-Host "    ██║ ╚████║╚██████╔╝   ██║   ██║██║        ██║   " -ForegroundColor Cyan
    Write-Host "    ╚═╝  ╚═══╝ ╚═════╝    ╚═╝   ╚═╝╚═╝        ╚═╝   " -ForegroundColor Cyan
    Write-Host ""
    Write-Host "    🎛️ Development Environment Manager" -ForegroundColor Yellow
    Write-Host "    🚀 Quick Setup • 🔧 Migrations • 🛑 Cleanup" -ForegroundColor Gray
    Write-Host ""
    Write-Host "═══════════════════════════════════════════════════════════════════════════════" -ForegroundColor DarkGray
}

function Show-Status {
    Write-Host ""
    Write-Host "    📊 Current Status:" -ForegroundColor White
    
    # Check Docker
    try {
        docker version | Out-Null 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "    ├─ 🐳 Docker: " -NoNewline -ForegroundColor Gray
            Write-Host "Running" -ForegroundColor Green
        } else {
            Write-Host "    ├─ 🐳 Docker: " -NoNewline -ForegroundColor Gray
            Write-Host "Not Available" -ForegroundColor Red
        }
    } catch {
        Write-Host "    ├─ 🐳 Docker: " -NoNewline -ForegroundColor Gray
        Write-Host "Not Available" -ForegroundColor Red
    }
    
    # Check PostgreSQL container
    $pgStatus = docker ps --filter "name=notify-postgres" --format "{{.Status}}" 2>$null
    if ($pgStatus) {
        Write-Host "    ├─ 🗄️  PostgreSQL: " -NoNewline -ForegroundColor Gray
        Write-Host "Running" -ForegroundColor Green
    } else {
        $pgStopped = docker ps -a --filter "name=notify-postgres" --format "{{.Status}}" 2>$null
        if ($pgStopped) {
            Write-Host "    ├─ 🗄️  PostgreSQL: " -NoNewline -ForegroundColor Gray
            Write-Host "Stopped" -ForegroundColor Yellow
        } else {
            Write-Host "    ├─ 🗄️  PostgreSQL: " -NoNewline -ForegroundColor Gray
            Write-Host "Not Created" -ForegroundColor Red
        }
    }
    
    # Check Redis container
    $redisStatus = docker ps --filter "name=notify-redis" --format "{{.Status}}" 2>$null
    if ($redisStatus) {
        Write-Host "    └─ 🔴 Redis: " -NoNewline -ForegroundColor Gray
        Write-Host "Running" -ForegroundColor Green
    } else {
        $redisStopped = docker ps -a --filter "name=notify-redis" --format "{{.Status}}" 2>$null
        if ($redisStopped) {
            Write-Host "    └─ 🔴 Redis: " -NoNewline -ForegroundColor Gray
            Write-Host "Stopped" -ForegroundColor Yellow
        } else {
            Write-Host "    └─ 🔴 Redis: " -NoNewline -ForegroundColor Gray
            Write-Host "Not Created" -ForegroundColor Red
        }
    }
}

function Show-Menu {
    Write-Host ""
    Write-Host "    🎯 Available Actions:" -ForegroundColor White
    Write-Host ""
    Write-Host "    [1] 🚀 Start Development Environment" -ForegroundColor Green
    Write-Host "        └─ Start PostgreSQL + Redis + Apply Migrations"
    Write-Host ""
    Write-Host "    [2] 🔄 Restart Services" -ForegroundColor Cyan
    Write-Host "        └─ Stop and start containers (preserve data)"
    Write-Host ""
    Write-Host "    [3] 🛑 Stop Services" -ForegroundColor Yellow
    Write-Host "        └─ Stop containers (preserve containers and data)"
    Write-Host ""
    Write-Host "    [4] 🗑️  Remove Containers" -ForegroundColor Red
    Write-Host "        └─ Stop and remove containers (preserve data)"
    Write-Host ""
    Write-Host "    [5] 💥 Full Cleanup" -ForegroundColor Magenta
    Write-Host "        └─ Remove containers and volumes (⚠️ DATA LOSS!)"
    Write-Host ""
    Write-Host "    [6] 🔧 Run Migrations Only" -ForegroundColor Blue
    Write-Host "        └─ Apply Entity Framework migrations"
    Write-Host ""
    Write-Host "    [7] 🏃‍♂️ Run API" -ForegroundColor Green
    Write-Host "        └─ Start the Notify Service API"
    Write-Host ""
    Write-Host "    [8] ⚙️  Configuration Manager" -ForegroundColor Blue
    Write-Host "        └─ Manage environment variables and secrets"
    Write-Host ""
    Write-Host "    [9] 🌐 Open Documentation" -ForegroundColor Cyan
    Write-Host "        └─ Open Scalar UI in browser"
    Write-Host ""
    Write-Host "    [0] 🚪 Exit" -ForegroundColor Gray
    Write-Host ""
}

function Execute-Action {
    param([string]$Choice)
    
    $scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
    
    switch ($Choice) {
        "1" {
            Write-Host "🚀 Starting development environment..." -ForegroundColor Green
            & "$scriptPath\start-dev-services.ps1"
            Pause
        }
        "2" {
            Write-Host "🔄 Restarting services..." -ForegroundColor Cyan
            & "$scriptPath\stop-dev-services.ps1"
            Start-Sleep -Seconds 2
            & "$scriptPath\start-dev-services.ps1"
            Pause
        }
        "3" {
            Write-Host "🛑 Stopping services..." -ForegroundColor Yellow
            & "$scriptPath\stop-dev-services.ps1"
            Pause
        }
        "4" {
            Write-Host "🗑️ Removing containers..." -ForegroundColor Red
            & "$scriptPath\stop-dev-services.ps1" -Remove
            Pause
        }
        "5" {
            Write-Host "💥 Full cleanup..." -ForegroundColor Magenta
            & "$scriptPath\stop-dev-services.ps1" -Remove -RemoveVolumes
            Pause
        }
        "6" {
            Write-Host "🔧 Running migrations..." -ForegroundColor Blue
            $projectRoot = Split-Path -Parent $scriptPath
            Set-Location $projectRoot
            
            Write-Host "Applying Data migrations..." -ForegroundColor Cyan
            dotnet ef database update --project Libraries\Data --startup-project Presentations\WebApi --context ApplicationDbContext
            
            Write-Host "Applying Identity migrations..." -ForegroundColor Cyan
            dotnet ef database update --project Libraries\Identity --startup-project Presentations\WebApi --context IdentityContext
            
            Pause
        }
        "7" {
            Write-Host "🏃‍♂️ Starting API..." -ForegroundColor Green
            $projectRoot = Split-Path -Parent $scriptPath
            Set-Location $projectRoot
            dotnet run --project Presentations\WebApi
        }
        "8" {
            Write-Host "⚙️ Opening configuration manager..." -ForegroundColor Blue
            & "$scriptPath\config-manager.ps1" -Interactive
            Pause
        }
        "9" {
            Write-Host "🌐 Opening documentation..." -ForegroundColor Cyan
            Start-Process "https://localhost:5001/scalar"
            Start-Sleep -Seconds 1
        }
        "0" {
            Write-Host "👋 Goodbye!" -ForegroundColor Gray
            exit 0
        }
        default {
            Write-Host "❌ Invalid choice. Please try again." -ForegroundColor Red
            Start-Sleep -Seconds 2
        }
    }
}

# Main loop
do {
    Show-Header
    Show-Status
    Show-Menu
    
    Write-Host "    👉 Enter your choice (0-9): " -NoNewline -ForegroundColor White
    $choice = Read-Host
    
    Execute-Action -Choice $choice
    
} while ($choice -ne "0")
