# Setup script for local development environment
# This script sets up PostgreSQL and Redis for local development

Write-Host "Setting up local development environment..." -ForegroundColor Green

# Check if PostgreSQL is running
Write-Host "Checking PostgreSQL..." -ForegroundColor Yellow
try {
    $pgResult = psql -U postgres -c "SELECT version();" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ PostgreSQL is running" -ForegroundColor Green

        # Create databases
        Write-Host "Creating databases..." -ForegroundColor Yellow
        psql -U postgres -f "scripts\create-databases.sql"

        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Databases created successfully" -ForegroundColor Green
        } else {
            Write-Host "⚠ Database creation failed or databases already exist" -ForegroundColor Yellow
        }
    } else {
        Write-Host "✗ PostgreSQL is not running or not accessible" -ForegroundColor Red
        Write-Host "Please install and start PostgreSQL, then run this script again" -ForegroundColor Yellow
        Write-Host "Download from: https://www.postgresql.org/download/" -ForegroundColor Cyan
    }
} catch {
    Write-Host "✗ PostgreSQL connection failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please install and start PostgreSQL, then run this script again" -ForegroundColor Yellow
    Write-Host "Download from: https://www.postgresql.org/download/" -ForegroundColor Cyan
}

# Check if Redis is running
Write-Host "Checking Redis..." -ForegroundColor Yellow
try {
    $redisResult = redis-cli ping 2>$null
    if ($LASTEXITCODE -eq 0 -and $redisResult -eq "PONG") {
        Write-Host "✓ Redis is running" -ForegroundColor Green
    } else {
        Write-Host "✗ Redis is not running or not accessible" -ForegroundColor Red
        Write-Host "Please install and start Redis, then run this script again" -ForegroundColor Yellow
        Write-Host "Windows: choco install redis-64" -ForegroundColor Cyan
        Write-Host "Or use Docker: docker run -d -p 6379:6379 redis:7-alpine" -ForegroundColor Cyan
    }
} catch {
    Write-Host "✗ Redis connection failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please install and start Redis, then run this script again" -ForegroundColor Yellow
    Write-Host "Windows: choco install redis-64" -ForegroundColor Cyan
    Write-Host "Or use Docker: docker run -d -p 6379:6379 redis:7-alpine" -ForegroundColor Cyan
}

# Check .NET SDK
Write-Host "Checking .NET SDK..." -ForegroundColor Yellow
try {
    $dotnetVersion = dotnet --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        if ($dotnetVersion -like "8.*") {
            Write-Host "✓ .NET 8 SDK is installed: $dotnetVersion" -ForegroundColor Green
        } else {
            Write-Host "⚠ .NET 8 SDK not found. Current version: $dotnetVersion" -ForegroundColor Yellow
            Write-Host "Please install .NET 8 SDK from: https://dotnet.microsoft.com/download/dotnet/8.0" -ForegroundColor Cyan
        }
    } else {
        Write-Host "✗ .NET SDK is not installed" -ForegroundColor Red
        Write-Host "Please install .NET 8 SDK from: https://dotnet.microsoft.com/download/dotnet/8.0" -ForegroundColor Cyan
    }
} catch {
    Write-Host "✗ .NET SDK check failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please install .NET 8 SDK from: https://dotnet.microsoft.com/download/dotnet/8.0" -ForegroundColor Cyan
}

Write-Host "`nSetup complete! You can now run the application with:" -ForegroundColor Green
Write-Host "dotnet run --project Presentations\WebApi" -ForegroundColor Cyan
