# Azure DevOps Deployment Template
# Reusable deployment steps for different environments

parameters:
- name: azureSubscription
  type: string
- name: webAppName
  type: string
- name: containerRegistry
  type: string
- name: imageRepository
  type: string
- name: tag
  type: string
- name: resourceGroupName
  type: string
  default: 'notify-service-rg'
- name: slotName
  type: string
  default: ''

steps:
# Download build artifacts
- task: DownloadBuildArtifacts@0
  displayName: 'Download build artifacts'
  inputs:
    buildType: 'current'
    downloadType: 'single'
    artifactName: 'drop'
    downloadPath: '$(System.ArtifactsDirectory)'

# Azure CLI - Update App Service configuration
- task: AzureCLI@2
  displayName: 'Configure App Service'
  inputs:
    azureSubscription: '${{ parameters.azureSubscription }}'
    scriptType: 'bash'
    scriptLocation: 'inlineScript'
    inlineScript: |
      # Set container settings
      az webapp config container set \
        --name ${{ parameters.webAppName }} \
        --resource-group ${{ parameters.resourceGroupName }} \
        --docker-custom-image-name ${{ parameters.containerRegistry }}/${{ parameters.imageRepository }}:${{ parameters.tag }} \
        --docker-registry-server-url https://${{ parameters.containerRegistry }}
      
      # Configure app settings
      az webapp config appsettings set \
        --name ${{ parameters.webAppName }} \
        --resource-group ${{ parameters.resourceGroupName }} \
        --settings \
          ASPNETCORE_ENVIRONMENT="Production" \
          WEBSITES_ENABLE_APP_SERVICE_STORAGE="false" \
          WEBSITES_PORT="80" \
          DOCKER_REGISTRY_SERVER_URL="https://${{ parameters.containerRegistry }}" \
          DOCKER_ENABLE_CI="true"

# Deploy to Azure Web App
- task: AzureWebAppContainer@1
  displayName: 'Deploy to Azure Web App'
  inputs:
    azureSubscription: '${{ parameters.azureSubscription }}'
    appName: '${{ parameters.webAppName }}'
    ${{ if ne(parameters.slotName, '') }}:
      slotName: '${{ parameters.slotName }}'
    containers: '${{ parameters.containerRegistry }}/${{ parameters.imageRepository }}:${{ parameters.tag }}'

# Run database migrations
- task: AzureCLI@2
  displayName: 'Run database migrations'
  inputs:
    azureSubscription: '${{ parameters.azureSubscription }}'
    scriptType: 'bash'
    scriptLocation: 'inlineScript'
    inlineScript: |
      # Get the web app URL
      WEB_APP_URL=$(az webapp show --name ${{ parameters.webAppName }} --resource-group ${{ parameters.resourceGroupName }} --query defaultHostName -o tsv)
      
      # Wait for app to be ready
      echo "Waiting for application to be ready..."
      for i in {1..30}; do
        if curl -f -s "https://${WEB_APP_URL}/health" > /dev/null; then
          echo "Application is ready!"
          break
        fi
        echo "Attempt $i: Application not ready yet, waiting 10 seconds..."
        sleep 10
      done
      
      # Trigger migration endpoint (if you have one)
      # curl -X POST "https://${WEB_APP_URL}/api/admin/migrate" -H "Authorization: Bearer ${MIGRATION_TOKEN}"

# Health check
- task: AzureCLI@2
  displayName: 'Health check'
  inputs:
    azureSubscription: '${{ parameters.azureSubscription }}'
    scriptType: 'bash'
    scriptLocation: 'inlineScript'
    inlineScript: |
      WEB_APP_URL=$(az webapp show --name ${{ parameters.webAppName }} --resource-group ${{ parameters.resourceGroupName }} --query defaultHostName -o tsv)
      
      echo "Performing health check on https://${WEB_APP_URL}/health"
      
      # Health check with retry
      for i in {1..5}; do
        if curl -f -s "https://${WEB_APP_URL}/health"; then
          echo "Health check passed!"
          exit 0
        fi
        echo "Health check attempt $i failed, retrying in 30 seconds..."
        sleep 30
      done
      
      echo "Health check failed after 5 attempts"
      exit 1

# Smoke tests
- task: AzureCLI@2
  displayName: 'Run smoke tests'
  inputs:
    azureSubscription: '${{ parameters.azureSubscription }}'
    scriptType: 'bash'
    scriptLocation: 'inlineScript'
    inlineScript: |
      WEB_APP_URL=$(az webapp show --name ${{ parameters.webAppName }} --resource-group ${{ parameters.resourceGroupName }} --query defaultHostName -o tsv)
      
      echo "Running smoke tests against https://${WEB_APP_URL}"
      
      # Test API documentation endpoint
      echo "Testing Scalar UI endpoint..."
      curl -f -s "https://${WEB_APP_URL}/scalar" > /dev/null || (echo "Scalar UI test failed" && exit 1)
      
      # Test OpenAPI endpoint
      echo "Testing OpenAPI endpoint..."
      curl -f -s "https://${WEB_APP_URL}/openapi/v1.json" > /dev/null || (echo "OpenAPI test failed" && exit 1)
      
      echo "All smoke tests passed!"

# Swap slots (if using deployment slots)
- ${{ if ne(parameters.slotName, '') }}:
  - task: AzureAppServiceManage@0
    displayName: 'Swap deployment slots'
    inputs:
      azureSubscription: '${{ parameters.azureSubscription }}'
      action: 'Swap Slots'
      webAppName: '${{ parameters.webAppName }}'
      resourceGroupName: '${{ parameters.resourceGroupName }}'
      sourceSlot: '${{ parameters.slotName }}'
      targetSlot: 'production'

# Send deployment notification (Optional)
- task: AzureCLI@2
  displayName: 'Send deployment notification'
  condition: always()
  inputs:
    azureSubscription: '${{ parameters.azureSubscription }}'
    scriptType: 'bash'
    scriptLocation: 'inlineScript'
    inlineScript: |
      WEB_APP_URL=$(az webapp show --name ${{ parameters.webAppName }} --resource-group ${{ parameters.resourceGroupName }} --query defaultHostName -o tsv)
      
      if [ "$(Agent.JobStatus)" = "Succeeded" ]; then
        STATUS="✅ SUCCESS"
        COLOR="good"
      else
        STATUS="❌ FAILED"
        COLOR="danger"
      fi
      
      # Send to Slack (if webhook configured)
      if [ ! -z "${SLACK_WEBHOOK_URL}" ]; then
        curl -X POST -H 'Content-type: application/json' \
          --data "{
            \"attachments\": [{
              \"color\": \"${COLOR}\",
              \"title\": \"Deployment ${STATUS}\",
              \"fields\": [
                {\"title\": \"Environment\", \"value\": \"${{ parameters.webAppName }}\", \"short\": true},
                {\"title\": \"Build\", \"value\": \"$(Build.BuildNumber)\", \"short\": true},
                {\"title\": \"URL\", \"value\": \"https://${WEB_APP_URL}\", \"short\": false}
              ]
            }]
          }" \
          ${SLACK_WEBHOOK_URL}
      fi
      
      echo "Deployment notification sent"
