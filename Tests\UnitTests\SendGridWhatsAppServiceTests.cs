using Models.DTOs.WhatsApp;
using Services.Concrete;
using Services.Interfaces;

namespace UnitTests;

public class SendGridWhatsAppServiceTests
{
    private readonly IMessageService<WhatsAppPayload, WhatsAppResult> _service;

    public SendGridWhatsAppServiceTests()
    {
        _service = new SendGridWhatsAppService(new HttpClient()); // Use the interface
    }

    [Fact]
    public async Task SendAsync_ShouldReturnSuccess()
    {
        var payload = new WhatsAppPayload
        {
            Sender = "TestSender",
            Recipient = "1234567890",
            Message = "Test Message"
        };

        var result = await _service.SendAsync(payload, CancellationToken.None);

        Assert.NotNull(result);
        Assert.True(result.IsSuccess); // Confirming success with simplified interface
    }
}