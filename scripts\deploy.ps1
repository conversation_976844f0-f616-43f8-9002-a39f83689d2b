# =========================
# PowerShell Deployment Script for Notify Service API
# Supports local Docker and Azure deployment
# =========================

param(
    [Parameter(Position=0)]
    [ValidateSet("local", "azure", "help")]
    [string]$Environment = "local"
)

# Configuration
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$DockerRegistry = "notifyregistry.azurecr.io"
$ImageName = "notify-service-api"

# Functions
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Test-Prerequisites {
    Write-Info "Checking prerequisites..."
    
    # Check Docker
    if (-not (Get-Command docker -ErrorAction SilentlyContinue)) {
        Write-Error "Docker is not installed"
        exit 1
    }
    
    # Check Docker Compose
    if (-not (Get-Command docker-compose -ErrorAction SilentlyContinue)) {
        Write-Error "Docker Compose is not installed"
        exit 1
    }
    
    # Check .env file
    $EnvFile = Join-Path $ProjectRoot ".env"
    if (-not (Test-Path $EnvFile)) {
        Write-Warning ".env file not found. Creating from template..."
        $TemplateFile = Join-Path $ProjectRoot ".env.template"
        Copy-Item $TemplateFile $EnvFile
        Write-Warning "Please update .env file with your configuration"
    }
    
    Write-Success "Prerequisites check completed"
}

function Build-Image {
    Write-Info "Building Docker image..."
    
    Set-Location $ProjectRoot
    
    $Timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
    
    # Build the image
    docker build `
        -f Dockerfile.production `
        -t "${ImageName}:latest" `
        -t "${ImageName}:$Timestamp" `
        --build-arg BUILD_CONFIGURATION=Release `
        .
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Docker build failed"
        exit 1
    }
    
    Write-Success "Docker image built successfully"
}

function Deploy-Local {
    Write-Info "Deploying to local environment..."
    
    Set-Location $ProjectRoot
    
    # Stop existing containers
    docker-compose down
    
    # Build and start services
    docker-compose up -d --build
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Docker Compose failed"
        exit 1
    }
    
    # Wait for services to be ready
    Write-Info "Waiting for services to be ready..."
    Start-Sleep -Seconds 30
    
    # Check health
    try {
        $Response = Invoke-WebRequest -Uri "http://localhost:8080/health" -UseBasicParsing -TimeoutSec 10
        if ($Response.StatusCode -eq 200) {
            Write-Success "Local deployment completed successfully"
            Write-Info "API available at: http://localhost:8080"
            Write-Info "Scalar UI available at: http://localhost:8080/scalar"
            Write-Info "Health check: http://localhost:8080/health"
        } else {
            throw "Health check returned status code: $($Response.StatusCode)"
        }
    }
    catch {
        Write-Error "Health check failed: $($_.Exception.Message)"
        docker-compose logs api
        exit 1
    }
}

function Deploy-Azure {
    Write-Info "Deploying to Azure..."
    
    # Check Azure CLI
    if (-not (Get-Command az -ErrorAction SilentlyContinue)) {
        Write-Error "Azure CLI is not installed"
        exit 1
    }
    
    # Login check
    try {
        az account show | Out-Null
    }
    catch {
        Write-Info "Please login to Azure..."
        az login
    }
    
    # Load environment variables
    $EnvFile = Join-Path $ProjectRoot ".env"
    if (Test-Path $EnvFile) {
        Get-Content $EnvFile | ForEach-Object {
            if ($_ -match '^([^#][^=]+)=(.*)$') {
                [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
            }
        }
    }
    
    $Timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
    
    # Tag image for Azure Container Registry
    docker tag "${ImageName}:latest" "${DockerRegistry}/${ImageName}:latest"
    docker tag "${ImageName}:latest" "${DockerRegistry}/${ImageName}:$Timestamp"
    
    # Login to ACR
    $RegistryName = $DockerRegistry.Split('.')[0]
    az acr login --name $RegistryName
    
    # Push image
    Write-Info "Pushing image to Azure Container Registry..."
    docker push "${DockerRegistry}/${ImageName}:latest"
    docker push "${DockerRegistry}/${ImageName}:$Timestamp"
    
    # Update Azure Web App
    Write-Info "Updating Azure Web App..."
    $WebAppName = $env:AZURE_WEBAPP_NAME
    $ResourceGroup = $env:AZURE_RESOURCE_GROUP
    
    az webapp config container set `
        --name $WebAppName `
        --resource-group $ResourceGroup `
        --docker-custom-image-name "${DockerRegistry}/${ImageName}:latest"
    
    # Restart the web app
    az webapp restart `
        --name $WebAppName `
        --resource-group $ResourceGroup
    
    # Get the URL
    $WebAppUrl = az webapp show `
        --name $WebAppName `
        --resource-group $ResourceGroup `
        --query defaultHostName -o tsv
    
    # Wait for deployment
    Write-Info "Waiting for deployment to complete..."
    Start-Sleep -Seconds 60
    
    # Health check
    try {
        $Response = Invoke-WebRequest -Uri "https://$WebAppUrl/health" -UseBasicParsing -TimeoutSec 30
        if ($Response.StatusCode -eq 200) {
            Write-Success "Azure deployment completed successfully"
            Write-Info "API available at: https://$WebAppUrl"
            Write-Info "Scalar UI available at: https://$WebAppUrl/scalar"
        } else {
            throw "Health check returned status code: $($Response.StatusCode)"
        }
    }
    catch {
        Write-Error "Health check failed: $($_.Exception.Message)"
        exit 1
    }
}

function Remove-OldImages {
    Write-Info "Cleaning up old Docker images..."
    
    # Get all images for this project
    $Images = docker images $ImageName --format "{{.Repository}}:{{.Tag}} {{.CreatedAt}}" | 
              Sort-Object { [DateTime]::Parse(($_ -split ' ', 2)[1]) } -Descending
    
    # Keep only the latest 5 images
    if ($Images.Count -gt 5) {
        $ImagesToRemove = $Images[5..($Images.Count - 1)]
        foreach ($Image in $ImagesToRemove) {
            $ImageTag = ($Image -split ' ')[0]
            docker rmi $ImageTag
        }
    }
    
    Write-Success "Cleanup completed"
}

function Show-Help {
    Write-Host @"
Usage: .\deploy.ps1 [ENVIRONMENT]

ENVIRONMENT:
  local    Deploy to local Docker environment (default)
  azure    Deploy to Azure Web App
  help     Show this help message

Examples:
  .\deploy.ps1 local    # Deploy locally
  .\deploy.ps1 azure    # Deploy to Azure

"@
}

# Main execution
switch ($Environment) {
    "local" {
        Test-Prerequisites
        Build-Image
        Deploy-Local
        Remove-OldImages
    }
    "azure" {
        Test-Prerequisites
        Build-Image
        Deploy-Azure
        Remove-OldImages
    }
    "help" {
        Show-Help
    }
    default {
        Write-Error "Unknown environment: $Environment"
        Show-Help
        exit 1
    }
}
